/*!
* inputmask.numeric.extensions.min.js
* https://github.com/RobinHerbots/Inputmask
* Copyright (c) 2010 - 2018 <PERSON>
* Licensed under the MIT license (http://www.opensource.org/licenses/mit-license.php)
* Version: 4.0.1-beta.3
*/

!function(e){"function"==typeof define&&define.amd?define(["./dependencyLibs/inputmask.dependencyLib","./inputmask"],e):"object"==typeof exports?module.exports=e(require("./dependencyLibs/inputmask.dependencyLib"),require("./inputmask")):e(window.dependencyLib||jQuery,window.Inputmask)}(function(e,i,r){function t(e,r){for(var t="",a=0;a<e.length;a++)i.prototype.definitions[e.charAt(a)]||r.definitions[e.charAt(a)]||r.optionalmarker.start===e.charAt(a)||r.optionalmarker.end===e.charAt(a)||r.quantifiermarker.start===e.charAt(a)||r.quantifiermarker.end===e.charAt(a)||r.groupmarker.start===e.charAt(a)||r.groupmarker.end===e.charAt(a)||r.alternatormarker===e.charAt(a)?t+="\\"+e.charAt(a):t+=e.charAt(a);return t}return i.extendAliases({numeric:{mask:function(e){if(0!==e.repeat&&isNaN(e.integerDigits)&&(e.integerDigits=e.repeat),e.repeat=0,e.groupSeparator===e.radixPoint&&e.digits&&"0"!==e.digits&&("."===e.radixPoint?e.groupSeparator=",":","===e.radixPoint?e.groupSeparator=".":e.groupSeparator="")," "===e.groupSeparator&&(e.skipOptionalPartCharacter=r),e.autoGroup=e.autoGroup&&""!==e.groupSeparator,e.autoGroup&&("string"==typeof e.groupSize&&isFinite(e.groupSize)&&(e.groupSize=parseInt(e.groupSize)),isFinite(e.integerDigits))){var i=Math.floor(e.integerDigits/e.groupSize),a=e.integerDigits%e.groupSize;e.integerDigits=parseInt(e.integerDigits)+(0===a?i-1:i),e.integerDigits<1&&(e.integerDigits="*")}e.placeholder.length>1&&(e.placeholder=e.placeholder.charAt(0)),"radixFocus"===e.positionCaretOnClick&&""===e.placeholder&&!1===e.integerOptional&&(e.positionCaretOnClick="lvp"),e.definitions[";"]=e.definitions["~"],e.definitions[";"].definitionSymbol="~",!0===e.numericInput&&(e.positionCaretOnClick="radixFocus"===e.positionCaretOnClick?"lvp":e.positionCaretOnClick,e.digitsOptional=!1,isNaN(e.digits)&&(e.digits=2),e.decimalProtect=!1);var n="[+]";if(n+=t(e.prefix,e),!0===e.integerOptional?n+="~{1,"+e.integerDigits+"}":n+="~{"+e.integerDigits+"}",e.digits!==r){var o=e.decimalProtect?":":e.radixPoint,p=e.digits.toString().split(",");isFinite(p[0])&&p[1]&&isFinite(p[1])?n+=o+";{"+e.digits+"}":(isNaN(e.digits)||parseInt(e.digits)>0)&&(e.digitsOptional?n+="["+o+";{1,"+e.digits+"}]":n+=o+";{"+e.digits+"}")}return n+=t(e.suffix,e),n+="[-]",e.greedy=!1,n},placeholder:"",greedy:!1,digits:"*",digitsOptional:!0,enforceDigitsOnBlur:!1,radixPoint:".",positionCaretOnClick:"radixFocus",groupSize:3,groupSeparator:"",autoGroup:!1,allowMinus:!0,negationSymbol:{front:"-",back:""},integerDigits:"+",integerOptional:!0,prefix:"",suffix:"",rightAlign:!0,decimalProtect:!0,min:null,max:null,step:1,insertMode:!0,autoUnmask:!1,unmaskAsNumber:!1,inputmode:"numeric",preValidation:function(i,t,a,n,o,p){if("-"===a||a===o.negationSymbol.front)return!0===o.allowMinus&&(o.isNegative=o.isNegative===r||!o.isNegative,""===i.join("")||{caret:t,dopost:!0});if(!1===n&&a===o.radixPoint&&o.digits!==r&&(isNaN(o.digits)||parseInt(o.digits)>0)){var s=e.inArray(o.radixPoint,i);if(-1!==s&&p.validPositions[s]!==r)return!0===o.numericInput?t===s:{caret:s+1}}return!0},postValidation:function(t,a,n){var o=n.suffix.split(""),p=n.prefix.split("");if(a.pos===r&&a.caret!==r&&!0!==a.dopost)return a;var s=a.caret!==r?a.caret:a.pos,l=t.slice();n.numericInput&&(s=l.length-s-1,l=l.reverse());var g=l[s];if(g===n.groupSeparator&&(g=l[s+=1]),s===l.length-n.suffix.length-1&&g===n.radixPoint)return a;g!==r&&g!==n.radixPoint&&g!==n.negationSymbol.front&&g!==n.negationSymbol.back&&(l[s]="?",n.prefix.length>0&&s>=(!1===n.isNegative?1:0)&&s<n.prefix.length-1+(!1===n.isNegative?1:0)?p[s-(!1===n.isNegative?1:0)]="?":n.suffix.length>0&&s>=l.length-n.suffix.length-(!1===n.isNegative?1:0)&&(o[s-(l.length-n.suffix.length-(!1===n.isNegative?1:0))]="?")),p=p.join(""),o=o.join("");var c=l.join("").replace(p,"");if(c=(c=(c=(c=c.replace(o,"")).replace(new RegExp(i.escapeRegex(n.groupSeparator),"g"),"")).replace(new RegExp("[-"+i.escapeRegex(n.negationSymbol.front)+"]","g"),"")).replace(new RegExp(i.escapeRegex(n.negationSymbol.back)+"$"),""),isNaN(n.placeholder)&&(c=c.replace(new RegExp(i.escapeRegex(n.placeholder),"g"),"")),c.length>1&&1!==c.indexOf(n.radixPoint)&&("0"===g&&(c=c.replace(/^\?/g,"")),c=c.replace(/^0/g,"")),c.charAt(0)===n.radixPoint&&""!==n.radixPoint&&!0!==n.numericInput&&(c="0"+c),""!==c){if(c=c.split(""),(!n.digitsOptional||n.enforceDigitsOnBlur&&"blur"===a.event)&&isFinite(n.digits)){var d=e.inArray(n.radixPoint,c),u=e.inArray(n.radixPoint,l);-1===d&&(c.push(n.radixPoint),d=c.length-1);for(var x=1;x<=n.digits;x++)n.digitsOptional&&(!n.enforceDigitsOnBlur||"blur"!==a.event)||c[d+x]!==r&&c[d+x]!==n.placeholder.charAt(0)?-1!==u&&l[u+x]!==r&&(c[d+x]=c[d+x]||l[u+x]):c[d+x]=a.placeholder||n.placeholder.charAt(0)}if(!0!==n.autoGroup||""===n.groupSeparator||g===n.radixPoint&&a.pos===r&&!a.dopost)c=c.join("");else{var f=c[c.length-1]===n.radixPoint&&a.c===n.radixPoint;c=i(function(e,i){var r="";if(r+="("+i.groupSeparator+"*{"+i.groupSize+"}){*}",""!==i.radixPoint){var t=e.join("").split(i.radixPoint);t[1]&&(r+=i.radixPoint+"*{"+t[1].match(/^\d*\??\d*/)[0].length+"}")}return r}(c,n),{numericInput:!0,jitMasking:!0,definitions:{"*":{validator:"[0-9?]",cardinality:1}}}).format(c.join("")),f&&(c+=n.radixPoint),c.charAt(0)===n.groupSeparator&&c.substr(1)}}if(n.isNegative&&"blur"===a.event&&(n.isNegative="0"!==c),c=p+c,c+=o,n.isNegative&&(c=n.negationSymbol.front+c,c+=n.negationSymbol.back),c=c.split(""),g!==r)if(g!==n.radixPoint&&g!==n.negationSymbol.front&&g!==n.negationSymbol.back)(s=e.inArray("?",c))>-1?c[s]=g:s=a.caret||0;else if(g===n.radixPoint||g===n.negationSymbol.front||g===n.negationSymbol.back){var m=e.inArray(g,c);-1!==m&&(s=m)}n.numericInput&&(s=c.length-s-1,c=c.reverse());var h={caret:g===r||a.pos!==r?s+(n.numericInput?-1:1):s,buffer:c,refreshFromBuffer:a.dopost||t.join("")!==c.join("")};return h.refreshFromBuffer?h:a},onBeforeWrite:function(t,a,n,o){if(t)switch(t.type){case"keydown":return o.postValidation(a,{caret:n,dopost:!0},o);case"blur":case"checkval":var p;if((s=o).parseMinMaxOptions===r&&(null!==s.min&&(s.min=s.min.toString().replace(new RegExp(i.escapeRegex(s.groupSeparator),"g"),""),","===s.radixPoint&&(s.min=s.min.replace(s.radixPoint,".")),s.min=isFinite(s.min)?parseFloat(s.min):NaN,isNaN(s.min)&&(s.min=Number.MIN_VALUE)),null!==s.max&&(s.max=s.max.toString().replace(new RegExp(i.escapeRegex(s.groupSeparator),"g"),""),","===s.radixPoint&&(s.max=s.max.replace(s.radixPoint,".")),s.max=isFinite(s.max)?parseFloat(s.max):NaN,isNaN(s.max)&&(s.max=Number.MAX_VALUE)),s.parseMinMaxOptions="done"),null!==o.min||null!==o.max){if(p=o.onUnMask(a.join(""),r,e.extend({},o,{unmaskAsNumber:!0})),null!==o.min&&p<o.min)return o.isNegative=o.min<0,o.postValidation(o.min.toString().replace(".",o.radixPoint).split(""),{caret:n,dopost:!0,placeholder:"0"},o);if(null!==o.max&&p>o.max)return o.isNegative=o.max<0,o.postValidation(o.max.toString().replace(".",o.radixPoint).split(""),{caret:n,dopost:!0,placeholder:"0"},o)}return o.postValidation(a,{caret:n,placeholder:"0",event:"blur"},o);case"_checkval":return{caret:n}}var s},regex:{integerPart:function(e,r){return r?new RegExp("["+i.escapeRegex(e.negationSymbol.front)+"+]?"):new RegExp("["+i.escapeRegex(e.negationSymbol.front)+"+]?\\d+")},integerNPart:function(e){return new RegExp("[\\d"+i.escapeRegex(e.groupSeparator)+i.escapeRegex(e.placeholder.charAt(0))+"]+")}},definitions:{"~":{validator:function(e,t,a,n,o,p){var s;if("k"===e||"m"===e){s={insert:[],c:0};for(var l=0,g="k"===e?2:5;l<g;l++)s.insert.push({pos:a+l,c:0});return s.pos=a+g,s}if(!0===(s=n?new RegExp("[0-9"+i.escapeRegex(o.groupSeparator)+"]").test(e):new RegExp("[0-9]").test(e))){if(!0!==o.numericInput&&t.validPositions[a]!==r&&"~"===t.validPositions[a].match.def&&!p){var c=t.buffer.join(""),d=(c=(c=c.replace(new RegExp("[-"+i.escapeRegex(o.negationSymbol.front)+"]","g"),"")).replace(new RegExp(i.escapeRegex(o.negationSymbol.back)+"$"),"")).split(o.radixPoint);d.length>1&&(d[1]=d[1].replace(/0/g,o.placeholder.charAt(0))),"0"===d[0]&&(d[0]=d[0].replace(/0/g,o.placeholder.charAt(0))),c=d[0]+o.radixPoint+d[1]||"";var u=t._buffer.join("");for(c===o.radixPoint&&(c=u);null===c.match(i.escapeRegex(u)+"$");)u=u.slice(1);s=(c=(c=c.replace(u,"")).split(""))[a]===r?{pos:a,remove:a}:{pos:a}}}else n||e!==o.radixPoint||t.validPositions[a-1]!==r||(s={insert:{pos:a,c:0},pos:a+1});return s},cardinality:1},"+":{validator:function(e,i,r,t,a){return a.allowMinus&&("-"===e||e===a.negationSymbol.front)},cardinality:1,placeholder:""},"-":{validator:function(e,i,r,t,a){return a.allowMinus&&e===a.negationSymbol.back},cardinality:1,placeholder:""},":":{validator:function(e,r,t,a,n){var o="["+i.escapeRegex(n.radixPoint)+"]",p=new RegExp(o).test(e);return p&&r.validPositions[t]&&r.validPositions[t].match.placeholder===n.radixPoint&&(p={caret:t+1}),p},cardinality:1,placeholder:function(e){return e.radixPoint}}},onUnMask:function(e,r,t){if(""===r&&!0===t.nullable)return r;var a=e.replace(t.prefix,"");return a=(a=a.replace(t.suffix,"")).replace(new RegExp(i.escapeRegex(t.groupSeparator),"g"),""),""!==t.placeholder.charAt(0)&&(a=a.replace(new RegExp(t.placeholder.charAt(0),"g"),"0")),t.unmaskAsNumber?(""!==t.radixPoint&&-1!==a.indexOf(t.radixPoint)&&(a=a.replace(i.escapeRegex.call(this,t.radixPoint),".")),a=(a=a.replace(new RegExp("^"+i.escapeRegex(t.negationSymbol.front)),"-")).replace(new RegExp(i.escapeRegex(t.negationSymbol.back)+"$"),""),Number(a)):a},isComplete:function(e,r){var t=(r.numericInput?e.slice().reverse():e).join("");return t=(t=(t=(t=(t=t.replace(new RegExp("^"+i.escapeRegex(r.negationSymbol.front)),"-")).replace(new RegExp(i.escapeRegex(r.negationSymbol.back)+"$"),"")).replace(r.prefix,"")).replace(r.suffix,"")).replace(new RegExp(i.escapeRegex(r.groupSeparator)+"([0-9]{3})","g"),"$1"),","===r.radixPoint&&(t=t.replace(i.escapeRegex(r.radixPoint),".")),isFinite(t)},onBeforeMask:function(t,a){if(a.isNegative=r,"number"==typeof t&&""!==a.radixPoint&&(t=t.toString().replace(".",a.radixPoint)),t=t.toString().charAt(t.length-1)===a.radixPoint?t.toString().substr(0,t.length-1):t.toString(),""!==a.radixPoint&&isFinite(t)){var n=t.split("."),o=""!==a.groupSeparator?parseInt(a.groupSize):0;2===n.length&&(n[0].length>o||n[1].length>o||n[0].length<=o&&n[1].length<o)&&(t=t.replace(".",a.radixPoint))}var p=t.match(/,/g),s=t.match(/\./g);if(t=s&&p?s.length>p.length?(t=t.replace(/\./g,"")).replace(",",a.radixPoint):p.length>s.length?(t=t.replace(/,/g,"")).replace(".",a.radixPoint):t.indexOf(".")<t.indexOf(",")?t.replace(/\./g,""):t.replace(/,/g,""):t.replace(new RegExp(i.escapeRegex(a.groupSeparator),"g"),""),0===a.digits&&(-1!==t.indexOf(".")?t=t.substring(0,t.indexOf(".")):-1!==t.indexOf(",")&&(t=t.substring(0,t.indexOf(",")))),""!==a.radixPoint&&isFinite(a.digits)&&-1!==t.indexOf(a.radixPoint)){var l=t.split(a.radixPoint)[1].match(new RegExp("\\d*"))[0];if(parseInt(a.digits)<l.toString().length){var g=Math.pow(10,parseInt(a.digits));t=t.replace(i.escapeRegex(a.radixPoint),"."),t=(t=Math.round(parseFloat(t)*g)/g).toString().replace(".",a.radixPoint)}}return function(i,r){if(r.numericInput){var t=e.inArray(r.radixPoint,i);-1===t&&(i.push(r.radixPoint),t=i.length-1);for(var a=1;a<=r.digits;a++)i[t+a]=i[t+a]||"0"}return i}(t.toString().split(""),a).join("")},onKeyDown:function(r,t,a,n){var o=e(this);if(r.ctrlKey)switch(r.keyCode){case i.keyCode.UP:o.val(parseFloat(this.inputmask.unmaskedvalue())+parseInt(n.step)),o.trigger("setvalue");break;case i.keyCode.DOWN:o.val(parseFloat(this.inputmask.unmaskedvalue())-parseInt(n.step)),o.trigger("setvalue")}}},currency:{prefix:"$ ",groupSeparator:",",alias:"numeric",placeholder:"0",autoGroup:!0,digits:2,digitsOptional:!1,clearMaskOnLostFocus:!1},decimal:{alias:"numeric"},integer:{alias:"numeric",digits:0,radixPoint:""},percentage:{alias:"numeric",digits:2,digitsOptional:!0,radixPoint:".",placeholder:"0",autoGroup:!1,min:0,max:100,suffix:" %",allowMinus:!1}}),i});