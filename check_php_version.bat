@echo off
setlocal enabledelayedexpansion
color 0A
title PHP Version Checker

echo ===============================================
echo           PHP VERSION CHECKER
echo ===============================================
echo.

echo Checking all available PHP versions on your system...
echo.

echo [1] System PATH PHP:
echo ===============================================
php --version 2>nul
if errorlevel 1 (
    echo ❌ No PHP found in system PATH
) else (
    echo ✅ PHP found in system PATH
)

echo.
echo [2] XAMPP PHP:
echo ===============================================
if exist "C:\xampp\php\php.exe" (
    echo ✅ XAMPP PHP found at C:\xampp\php\php.exe
    "C:\xampp\php\php.exe" --version
) else (
    echo ❌ No XAMPP PHP found at C:\xampp\php\php.exe
)

echo.
echo [3] Alternative XAMPP Locations:
echo ===============================================

:: Check common alternative locations
set "locations=C:\xampp-8.2 C:\xampp82 C:\Program Files\xampp C:\Program Files (x86)\xampp"

for %%L in (%locations%) do (
    if exist "%%L\php\php.exe" (
        echo ✅ Found PHP at: %%L\php\php.exe
        "%%L\php\php.exe" --version | findstr "PHP"
    )
)

echo.
echo [4] Laravel Requirement Check:
echo ===============================================

echo Laravel 12 requires PHP 8.2.0 or higher
echo.

if exist "C:\xampp\php\php.exe" (
    for /f "tokens=2 delims= " %%a in ('"C:\xampp\php\php.exe" --version ^| findstr "PHP"') do (
        set CURRENT_VERSION=%%a
        goto :version_found
    )
    :version_found
    
    echo Current XAMPP PHP: !CURRENT_VERSION!
    
    echo !CURRENT_VERSION! | findstr /r "^8\.[2-9]\|^8\.[1-9][0-9]\|^9\." >nul
    if errorlevel 1 (
        echo ❌ INCOMPATIBLE: PHP !CURRENT_VERSION! is too old
        echo.
        echo SOLUTION NEEDED:
        echo 1. Download XAMPP 8.2.12 or higher
        echo 2. Uninstall current XAMPP
        echo 3. Install new XAMPP
        echo 4. Run upgrade_xampp.bat
    ) else (
        echo ✅ COMPATIBLE: PHP !CURRENT_VERSION! meets requirements
    )
) else (
    echo ❌ Cannot check - XAMPP PHP not found
)

echo.
echo [5] Composer Platform Check:
echo ===============================================

if exist "C:\xampp\htdocs\main\vendor\composer\platform_check.php" (
    echo Testing Composer platform check...
    "C:\xampp\php\php.exe" "C:\xampp\htdocs\main\vendor\composer\platform_check.php" 2>&1
    if errorlevel 1 (
        echo.
        echo ❌ This is the error you're experiencing
        echo The Composer dependencies require PHP 8.2+ but XAMPP has an older version
    ) else (
        echo ✅ Composer platform check passed
    )
) else (
    echo Platform check file not found
)

echo.
echo ===============================================
echo              RECOMMENDATIONS
echo ===============================================
echo.

if exist "C:\xampp\php\php.exe" (
    "C:\xampp\php\php.exe" --version | findstr "8.0" >nul
    if not errorlevel 1 (
        echo 🔧 IMMEDIATE ACTION REQUIRED:
        echo.
        echo You're running PHP 8.0.x but need PHP 8.2+
        echo.
        echo SOLUTION:
        echo 1. Run: upgrade_xampp.bat
        echo 2. Follow the upgrade instructions
        echo 3. Install XAMPP 8.2.12 or higher
        echo 4. Then run: START_HERE.bat
        echo.
    ) else (
        echo ✅ Your PHP version should be compatible
        echo If you're still getting errors, try:
        echo 1. Restart XAMPP services
        echo 2. Run: troubleshoot.bat
        echo 3. Clear Composer cache: composer clear-cache
    )
) else (
    echo 🔧 XAMPP NOT FOUND:
    echo.
    echo Please install XAMPP 8.2.12 or higher first
    echo Download from: https://www.apachefriends.org/download.html
)

echo.
pause
