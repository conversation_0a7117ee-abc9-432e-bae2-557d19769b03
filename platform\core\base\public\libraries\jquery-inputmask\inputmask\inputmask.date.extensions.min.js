/*!
* inputmask.date.extensions.min.js
* https://github.com/RobinHerbots/Inputmask
* Copyright (c) 2010 - 2018 <PERSON>
* Licensed under the MIT license (http://www.opensource.org/licenses/mit-license.php)
* Version: 4.0.1-beta.3
*/

!function(t){"function"==typeof define&&define.amd?define(["./dependencyLibs/inputmask.dependencyLib","./inputmask"],t):"object"==typeof exports?module.exports=t(require("./dependencyLibs/inputmask.dependencyLib"),require("./inputmask")):t(window.dependencyLib||jQuery,window.Inputmask)}(function(t,e){var o={d:["[1-9]|[12][0-9]|3[01]",Date.prototype.setDate,"day",Date.prototype.getDate],dd:["0[1-9]|[12][0-9]|3[01]",Date.prototype.setDate,"day",function(){return i(Date.prototype.getDate.call(this),2)}],ddd:[""],dddd:[""],m:["[1-9]|1[012]",Date.prototype.setMonth,"month",function(){return Date.prototype.getMonth.call(this)+1}],mm:["0[1-9]|1[012]",Date.prototype.setMonth,"month",function(){return i(Date.prototype.getMonth.call(this)+1,2)}],mmm:[""],mmmm:[""],yy:["[0-9]{2}",Date.prototype.setFullYear,"year",function(){return i(Date.prototype.getFullYear.call(this),2)}],yyyy:["[0-9]{4}",Date.prototype.setFullYear,"year",function(){return i(Date.prototype.getFullYear.call(this),4)}],h:["[1-9]|1[0-2]",Date.prototype.setHours,"hours",Date.prototype.getHours],hh:["0[1-9]|1[0-2]",Date.prototype.setHours,"hours",function(){return i(Date.prototype.getHours.call(this),2)}],hhh:["[0-9]+",Date.prototype.setHours,"hours",Date.prototype.getHours],H:["1?[0-9]|2[0-3]",Date.prototype.setHours,"hours",Date.prototype.getHours],HH:["[01][0-9]|2[0-3]",Date.prototype.setHours,"hours",function(){return i(Date.prototype.getHours.call(this),2)}],HHH:["[0-9]+",Date.prototype.setHours,"hours",Date.prototype.getHours],M:["[1-5]?[0-9]",Date.prototype.setMinutes,"minutes",Date.prototype.getMinutes],MM:["[0-5][0-9]",Date.prototype.setMinutes,"minutes",function(){return i(Date.prototype.getMinutes.call(this),2)}],s:["[1-5]?[0-9]",Date.prototype.setSeconds,"seconds",Date.prototype.getSeconds],ss:["[0-5][0-9]",Date.prototype.setSeconds,"seconds",function(){return i(Date.prototype.getSeconds.call(this),2)}],l:["[0-9]{3}",Date.prototype.setMilliseconds,"milliseconds",function(){return i(Date.prototype.getMilliseconds.call(this),3)}],L:["[0-9]{2}",Date.prototype.setMilliseconds,"milliseconds",function(){return i(Date.prototype.getMilliseconds.call(this),2)}],t:["[ap]"],tt:["[ap]m"],T:["[AP]"],TT:["[AP]M"],Z:[""],o:[""],S:[""]},r={isoDate:"yyyy-mm-dd",isoTime:"HH:MM:ss",isoDateTime:"yyyy-mm-dd'T'HH:MM:ss",isoUtcDateTime:"UTC:yyyy-mm-dd'T'HH:MM:ss'Z'"};function a(t){if(!t.tokenizer){var e=[];for(var r in o)-1===e.indexOf(r[0])&&e.push(r[0]);t.tokenizer="("+e.join("+|")+")+?|.",t.tokenizer=new RegExp(t.tokenizer,"g")}return t.tokenizer}function n(t,r,n){for(var i,s="";i=a(n).exec(t);){if(void 0===r)if(o[i[0]])s+="("+o[i[0]][0]+")";else switch(i[0]){case"[":s+="(";break;case"]":s+=")?";break;default:s+=e.escapeRegex(i[0])}else if(o[i[0]])s+=o[i[0]][3].call(r.date);else s+=i[0]}return s}function i(t,e){for(t=String(t),e=e||2;t.length<e;)t="0"+t;return t}function s(t,e,r){var n,i,s,u,p={date:new Date(1,0,1)},d=t;function m(t,e,o){t[n]=function(t){var e;if(r.min&&r.min[n]||r.max&&r.max[n]){var o=r.min&&r.min[n]||r.max[n],a=r.max&&r.max[n]||r.min[n];for(e=t.replace(/[^0-9]/g,""),e+=(o.indexOf(e)<a.indexOf(e)?a:o).toString().substr(e.length);!new RegExp(u).test(e);)e--}else e=t.replace(/[^0-9]/g,"0");return e}(e),t["raw"+n]=e,void 0!==s&&s.call(t.date,"month"==n?parseInt(t[n])-1:t[n])}if("string"==typeof d){for(;i=a(r).exec(e);){var y=d.slice(0,i[0].length);o.hasOwnProperty(i[0])&&(u=o[i[0]][0],n=o[i[0]][2],s=o[i[0]][1],m(p,y)),d=d.slice(y.length)}return p}}return e.extendAliases({datetime:{mask:function(t){return o.S=t.i18n.ordinalSuffix.join("|"),t.inputFormat=r[t.inputFormat]||t.inputFormat,t.displayFormat=r[t.displayFormat]||t.displayFormat||t.inputFormat,t.outputFormat=r[t.outputFormat]||t.outputFormat||t.inputFormat,t.placeholder=""!==t.placeholder?t.placeholder:t.inputFormat.replace(/[\[\]]/,""),t.min=s(t.min,t.inputFormat,t),t.max=s(t.max,t.inputFormat,t),t.regex=n(t.inputFormat,void 0,t),null},placeholder:"",inputFormat:"isoDateTime",displayFormat:void 0,outputFormat:void 0,min:null,max:null,i18n:{dayNames:["Mon","Tue","Wed","Thu","Fri","Sat","Sun","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"],monthNames:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec","January","February","March","April","May","June","July","August","September","October","November","December"],ordinalSuffix:["st","nd","rd","th"]},postValidation:function(t,e,o){var r,a,n=e,i=s(t.join(""),o.inputFormat,o);return n&&i.date.getTime()==i.date.getTime()&&(r=i,a=n,n=(n=(!isFinite(r.rawday)||"29"==r.day&&!isFinite(r.rawyear)||new Date(r.date.getFullYear(),isFinite(r.rawmonth)?r.month:r.date.getMonth()+1,0).getDate()>=r.day)&&a)&&function(t,e){var o=!0;if(e.min){if(t.rawyear){var r=t.rawyear.replace(/[^0-9]/g,"");o=e.min.year.substr(0,r.length)<=r}t.year===t.rawyear&&e.min.date.getTime()==e.min.date.getTime()&&(o=e.min.date.getTime()<=t.date.getTime())}return o&&e.max&&e.max.date.getTime()==e.max.date.getTime()&&(o=e.max.date.getTime()>=t.date.getTime()),o}(i,o)),n},onKeyDown:function(o,r,n,s){if(o.ctrlKey&&o.keyCode===e.keyCode.RIGHT){for(var u,p=new Date,d="";u=a(s).exec(s.inputFormat);)"d"===u[0].charAt(0)?d+=i(p.getDate(),u[0].length):"m"===u[0].charAt(0)?d+=i(p.getMonth()+1,u[0].length):"yyyy"===u[0]?d+=p.getFullYear().toString():"y"===u[0].charAt(0)&&(d+=i(p.getYear(),u[0].length));this.inputmask._valueSet(d),t(this).trigger("setvalue")}},onUnMask:function(t,e,o){return n(o.outputFormat,s(t,o.inputFormat,o),o)},casing:function(t,e,o,r){return 0==e.nativeDef.indexOf("[ap]")?t.toLowerCase():0==e.nativeDef.indexOf("[AP]")?t.toUpperCase():t},insertMode:!1}}),e});