@echo off
setlocal enabledelayedexpansion
color 0A
title Laravel Botble CMS - Complete Setup Automation

echo ===============================================
echo    LARAVEL BOTBLE CMS SETUP AUTOMATION
echo ===============================================
echo.

:: Set variables
set "APP_PATH=c:\xampp\htdocs\main"
set "XAMPP_PATH=c:\xampp"
set "APP_URL=http://localhost/main"
set "ADMIN_URL=http://localhost/main/admin"
set "LOG_FILE=%APP_PATH%\setup_log.txt"

:: Create log file
echo Setup started at %date% %time% > "%LOG_FILE%"

echo [STEP 1] Verifying XAMPP Installation and PHP Version...
echo ===============================================

:: Check if XAMPP is installed
if not exist "%XAMPP_PATH%\php\php.exe" (
    echo ERROR: XAMPP not found at %XAMPP_PATH%
    echo Please install XAMPP first.
    pause
    exit /b 1
)

:: Check PHP version
echo Checking PHP version...
"%XAMPP_PATH%\php\php.exe" --version
if errorlevel 1 (
    echo ERROR: PHP not working properly
    pause
    exit /b 1
)

:: Get PHP version and check if it's 8.2+
for /f "tokens=2 delims= " %%a in ('"%XAMPP_PATH%\php\php.exe" --version ^| findstr "PHP"') do (
    set PHP_VERSION=%%a
    goto :php_version_found
)
:php_version_found

echo PHP Version: %PHP_VERSION%
echo PHP Version: %PHP_VERSION% >> "%LOG_FILE%"

:: Check if PHP version is 8.2 or higher
echo %PHP_VERSION% | findstr /r "^8\.[2-9]\|^8\.[1-9][0-9]\|^9\." >nul
if errorlevel 1 (
    echo ERROR: PHP version %PHP_VERSION% is too old. Laravel requires PHP 8.2+
    echo Please install XAMPP with PHP 8.2 or higher.
    pause
    exit /b 1
)

echo ✓ PHP version %PHP_VERSION% is compatible
echo.

echo [STEP 2] Checking XAMPP Services...
echo ===============================================

:: Check if Apache is running
netstat -an | findstr ":80 " >nul
if errorlevel 1 (
    echo WARNING: Apache might not be running on port 80
    echo Please start Apache in XAMPP Control Panel
    echo.
) else (
    echo ✓ Apache appears to be running on port 80
)

:: Check if MySQL is running
netstat -an | findstr ":3306 " >nul
if errorlevel 1 (
    echo WARNING: MySQL might not be running on port 3306
    echo Please start MySQL in XAMPP Control Panel if you plan to use MySQL
    echo.
) else (
    echo ✓ MySQL appears to be running on port 3306
)

echo.

echo [STEP 3] Verifying Laravel Application Structure...
echo ===============================================

cd /d "%APP_PATH%"
if errorlevel 1 (
    echo ERROR: Cannot access application directory: %APP_PATH%
    pause
    exit /b 1
)

:: Check essential Laravel files
if not exist "artisan" (
    echo ERROR: artisan file not found. This doesn't appear to be a Laravel application.
    pause
    exit /b 1
)

if not exist "composer.json" (
    echo ERROR: composer.json not found.
    pause
    exit /b 1
)

if not exist ".env" (
    echo ERROR: .env file not found.
    pause
    exit /b 1
)

echo ✓ Laravel application structure verified
echo.

echo [STEP 4] Checking Composer and Dependencies...
echo ===============================================

:: Check if composer is available
composer --version >nul 2>&1
if errorlevel 1 (
    echo WARNING: Composer not found in PATH. Trying XAMPP's composer...
    if exist "%XAMPP_PATH%\composer\composer.bat" (
        set "COMPOSER_CMD=%XAMPP_PATH%\composer\composer.bat"
    ) else (
        echo ERROR: Composer not found. Please install Composer.
        pause
        exit /b 1
    )
) else (
    set "COMPOSER_CMD=composer"
    echo ✓ Composer is available
)

:: Check vendor directory
if not exist "vendor" (
    echo Installing Composer dependencies...
    %COMPOSER_CMD% install --no-dev --optimize-autoloader
    if errorlevel 1 (
        echo ERROR: Failed to install dependencies
        pause
        exit /b 1
    )
) else (
    echo ✓ Vendor directory exists
)

echo.

echo [STEP 5] Laravel Application Configuration...
echo ===============================================

:: Use XAMPP's PHP for artisan commands
set "PHP_CMD=%XAMPP_PATH%\php\php.exe"

:: Check Laravel version
echo Checking Laravel version...
%PHP_CMD% artisan --version
echo.

:: Clear and cache configuration
echo Clearing and caching configuration...
%PHP_CMD% artisan config:clear
%PHP_CMD% artisan cache:clear
%PHP_CMD% artisan route:clear
%PHP_CMD% artisan view:clear

echo Caching configuration for production...
%PHP_CMD% artisan config:cache
%PHP_CMD% artisan route:cache

echo ✓ Laravel configuration updated
echo.

echo [STEP 6] Database Setup and Migrations...
echo ===============================================

:: Check database configuration
echo Checking database configuration...
%PHP_CMD% artisan config:show database.default
echo.

:: Check if database file exists (for SQLite)
if exist "database\database.sqlite" (
    echo ✓ SQLite database file exists
) else (
    echo Creating SQLite database file...
    type nul > "database\database.sqlite"
    echo ✓ SQLite database file created
)

:: Run migrations
echo Running database migrations...
%PHP_CMD% artisan migrate:status
echo.

echo Checking if migrations need to be run...
%PHP_CMD% artisan migrate --force
if errorlevel 1 (
    echo WARNING: Some migrations failed. This might be normal for existing installations.
) else (
    echo ✓ Database migrations completed
)

echo.

echo [STEP 7] Testing Application Accessibility...
echo ===============================================

:: Test if application is accessible via Apache
echo Testing application accessibility at %APP_URL%...

:: Create a simple test using PowerShell
powershell -Command "try { $response = Invoke-WebRequest -Uri '%APP_URL%' -UseBasicParsing -TimeoutSec 10; Write-Host 'Status Code:' $response.StatusCode; if($response.StatusCode -eq 200) { Write-Host '✓ Application is accessible via Apache' } else { Write-Host 'WARNING: Unexpected status code' } } catch { Write-Host 'ERROR: Cannot access application -' $_.Exception.Message }"

echo.

echo [STEP 8] Testing Admin Panel...
echo ===============================================

echo Testing admin panel accessibility at %ADMIN_URL%...

powershell -Command "try { $response = Invoke-WebRequest -Uri '%ADMIN_URL%' -UseBasicParsing -TimeoutSec 10; Write-Host 'Admin Panel Status Code:' $response.StatusCode; if($response.StatusCode -eq 200) { Write-Host '✓ Admin panel is accessible' } else { Write-Host 'WARNING: Admin panel returned status code:' $response.StatusCode } } catch { Write-Host 'ERROR: Cannot access admin panel -' $_.Exception.Message }"

echo.

echo [STEP 9] Checking Botble CMS Installation...
echo ===============================================

:: Check if CMS is installed
echo Checking CMS installation status...
%PHP_CMD% artisan cms:install:check 2>nul
if errorlevel 1 (
    echo CMS installation check command not available or installation needed
    echo.
    echo Checking if installation is required...

    :: Check for admin user in database
    %PHP_CMD% artisan tinker --execute="echo App\Models\User::where('email', '<EMAIL>')->exists() ? 'ADMIN_EXISTS' : 'NO_ADMIN';" 2>nul | findstr "ADMIN_EXISTS" >nul
    if errorlevel 1 (
        echo.
        echo ⚠️  MANUAL INTERVENTION REQUIRED ⚠️
        echo ===============================================
        echo It appears the CMS needs to be set up. You have two options:
        echo.
        echo OPTION 1: Use the web installer
        echo   1. Open your browser and go to: %APP_URL%
        echo   2. Follow the installation wizard
        echo.
        echo OPTION 2: Use command line installation
        echo   Run: php artisan cms:install
        echo.
        echo After installation, the default admin credentials are usually:
        echo   Email: <EMAIL>
        echo   Password: 159357
        echo.
    ) else (
        echo ✓ Admin user exists - CMS appears to be installed
    )
) else (
    echo ✓ CMS installation verified
)

echo.

echo [STEP 10] Final System Check...
echo ===============================================

echo Performing final system checks...

:: Check storage permissions
if exist "storage" (
    echo ✓ Storage directory exists
) else (
    echo ERROR: Storage directory missing
)

:: Check if key is generated
findstr "APP_KEY=base64:" .env >nul
if errorlevel 1 (
    echo Generating application key...
    %PHP_CMD% artisan key:generate --force
) else (
    echo ✓ Application key is set
)

:: Check bootstrap cache
if exist "bootstrap\cache" (
    echo ✓ Bootstrap cache directory exists
) else (
    echo Creating bootstrap cache directory...
    mkdir "bootstrap\cache"
)

echo.
echo ===============================================
echo           SETUP COMPLETION SUMMARY
echo ===============================================
echo.
echo ✓ XAMPP Installation: Verified
echo ✓ PHP Version: %PHP_VERSION% (Compatible)
echo ✓ Laravel Application: Ready
echo ✓ Database: Configured
echo ✓ Dependencies: Installed
echo.
echo 🌐 APPLICATION URLS:
echo ===============================================
echo Main Application: %APP_URL%
echo Admin Panel:      %ADMIN_URL%
echo PHP Info:         %APP_URL%/phpinfo.php
echo.
echo 🔑 DEFAULT ADMIN CREDENTIALS (if not changed):
echo ===============================================
echo Email:    <EMAIL>
echo Password: 159357
echo.
echo 📋 NEXT STEPS:
echo ===============================================
echo 1. Open your browser and visit: %APP_URL%
echo 2. If you see an installation page, complete the setup
echo 3. Access admin panel at: %ADMIN_URL%
echo 4. Login with the credentials above
echo.
echo 🔧 TROUBLESHOOTING:
echo ===============================================
echo - If site doesn't load: Check XAMPP Apache is running
echo - If database errors: Check MySQL is running (if using MySQL)
echo - If permission errors: Run XAMPP as Administrator
echo - Check setup log: %LOG_FILE%
echo.

echo Setup completed at %date% %time% >> "%LOG_FILE%"
echo.
echo Press any key to open the application in your browser...
pause >nul

:: Open application in browser
start "" "%APP_URL%"

echo.
echo Setup automation complete!
pause
