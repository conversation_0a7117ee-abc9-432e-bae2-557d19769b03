<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table): void {
            $table->dropColumn('name');
        });

        Schema::table('users', function (Blueprint $table): void {
            $table->string('first_name', 120)->nullable();
            $table->string('last_name', 120)->nullable();
            $table->string('username', 60)->unique()->nullable();
            $table->string('password', 120)->nullable()->change();
            $table->foreignId('avatar_id')->nullable();
            $table->boolean('super_user')->default(0);
            $table->boolean('manage_supers')->default(0);
            $table->text('permissions')->nullable();
            $table->timestamp('last_login')->nullable();
        });

        Schema::create('activations', function (Blueprint $table): void {
            $table->id();
            $table->foreignId('user_id')->index();
            $table->string('code', 120);
            $table->boolean('completed')->default(0);
            $table->timestamp('completed_at')->nullable();
            $table->timestamps();
        });

        Schema::create('roles', function (Blueprint $table): void {
            $table->id();
            $table->string('slug', 120)->unique();
            $table->string('name', 120);
            $table->text('permissions')->nullable();
            $table->string('description', 400)->nullable();
            $table->tinyInteger('is_default')->unsigned()->default(0);
            $table->foreignId('created_by')->index();
            $table->foreignId('updated_by')->index();
            $table->timestamps();
        });

        Schema::create('role_users', function (Blueprint $table): void {
            $table->id();
            $table->foreignId('user_id')->index();
            $table->foreignId('role_id')->index();
            $table->nullableTimestamps();
        });

        Schema::create('user_meta', function (Blueprint $table): void {
            $table->id();
            $table->string('key', 120)->nullable();
            $table->string('value')->nullable();
            $table->foreignId('user_id')->index();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('activations');
        Schema::dropIfExists('roles');
        Schema::dropIfExists('role_users');
        Schema::dropIfExists('users');
        Schema::dropIfExists('user_meta');
    }
};
