<?php

namespace Bo<PERSON>ble\ACL\Http\Requests;

use Botble\Base\Rules\EmailRule;
use Bo<PERSON>ble\Support\Http\Requests\Request;

class ResetPasswordRequest extends Request
{
    public function rules(): array
    {
        return [
            'token' => ['required', 'string'],
            'email' => ['required', new EmailRule()],
            'password' => ['required', 'confirmed', 'min:6'],
        ];
    }
}
