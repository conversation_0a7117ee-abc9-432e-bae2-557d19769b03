@echo off
setlocal enabledelayedexpansion
color 0E
title XAMPP Upgrade Helper - PHP 8.0 to 8.2+

echo ===============================================
echo        XAMPP UPGRADE HELPER
echo ===============================================
echo.
echo Current Issue: PHP 8.0.30 detected, but <PERSON>vel requires PHP 8.2+
echo.
echo This script will help you:
echo 1. Stop the old XAMPP services
echo 2. Install the new XAMPP version
echo 3. Migrate your application
echo 4. Start the new XAMPP services
echo.

set "OLD_XAMPP=C:\xampp"
set "APP_PATH=C:\xampp\htdocs\main"

echo [STEP 1] Checking Current XAMPP Installation
echo ===============================================

if exist "%OLD_XAMPP%\php\php.exe" (
    echo Current XAMPP found at: %OLD_XAMPP%
    echo Current PHP version:
    "%OLD_XAMPP%\php\php.exe" --version | findstr "PHP"
    echo.
) else (
    echo No XAMPP installation found at %OLD_XAMPP%
)

echo [STEP 2] Stopping Current XAMPP Services
echo ===============================================

echo Attempting to stop Apache...
taskkill /F /IM httpd.exe 2>nul
if errorlevel 1 (
    echo Apache was not running or already stopped
) else (
    echo ✓ Apache stopped
)

echo Attempting to stop MySQL...
taskkill /F /IM mysqld.exe 2>nul
if errorlevel 1 (
    echo MySQL was not running or already stopped
) else (
    echo ✓ MySQL stopped
)

echo.
echo [STEP 3] Backup Current Application (Optional)
echo ===============================================

set /p backup="Do you want to backup your application? (Y/N): "
if /i "%backup%"=="Y" (
    set "BACKUP_DIR=C:\xampp_backup_%date:~-4,4%%date:~-10,2%%date:~-7,2%"
    echo Creating backup at: !BACKUP_DIR!
    
    if exist "%APP_PATH%" (
        mkdir "!BACKUP_DIR!" 2>nul
        xcopy "%APP_PATH%" "!BACKUP_DIR!\main" /E /I /H /Y
        echo ✓ Application backed up to: !BACKUP_DIR!\main
    ) else (
        echo Application directory not found, skipping backup
    )
)

echo.
echo [STEP 4] Installation Instructions
echo ===============================================
echo.
echo MANUAL STEPS REQUIRED:
echo.
echo 1. UNINSTALL OLD XAMPP (Recommended):
echo    - Go to Control Panel → Programs → Uninstall XAMPP
echo    - Or manually delete C:\xampp folder (after stopping services)
echo.
echo 2. INSTALL NEW XAMPP:
echo    - Run the XAMPP installer you downloaded
echo    - Install to C:\xampp (same location)
echo    - Select these components:
echo      ✓ Apache
echo      ✓ MySQL  
echo      ✓ PHP
echo      ✓ phpMyAdmin
echo      ✓ Perl (optional)
echo.
echo 3. AFTER INSTALLATION:
echo    - Start XAMPP Control Panel
echo    - Start Apache service
echo    - Start MySQL service
echo    - Come back and press any key to continue...
echo.

echo Press any key when you've completed the XAMPP installation...
pause >nul

echo.
echo [STEP 5] Verifying New Installation
echo ===============================================

if not exist "%OLD_XAMPP%\php\php.exe" (
    echo ERROR: XAMPP PHP not found. Please ensure XAMPP is installed correctly.
    echo Expected location: %OLD_XAMPP%\php\php.exe
    pause
    exit /b 1
)

echo Checking new PHP version...
"%OLD_XAMPP%\php\php.exe" --version
echo.

:: Check if PHP version is 8.2+
for /f "tokens=2 delims= " %%a in ('"%OLD_XAMPP%\php\php.exe" --version ^| findstr "PHP"') do (
    set NEW_PHP_VERSION=%%a
    goto :php_version_found
)
:php_version_found

echo New PHP Version: %NEW_PHP_VERSION%

:: Verify PHP version is 8.2 or higher
echo %NEW_PHP_VERSION% | findstr /r "^8\.[2-9]\|^8\.[1-9][0-9]\|^9\." >nul
if errorlevel 1 (
    echo.
    echo ❌ ERROR: Still running PHP %NEW_PHP_VERSION%
    echo The new XAMPP installation may not be active.
    echo.
    echo TROUBLESHOOTING:
    echo 1. Make sure you installed XAMPP 8.2.12 or higher
    echo 2. Restart your computer
    echo 3. Check XAMPP Control Panel shows the services running
    echo 4. Try running this script again
    echo.
    pause
    exit /b 1
) else (
    echo ✅ SUCCESS: PHP %NEW_PHP_VERSION% is compatible with Laravel!
)

echo.
echo [STEP 6] Testing Application Access
echo ===============================================

if not exist "%APP_PATH%" (
    echo ❌ Application not found at %APP_PATH%
    echo Please ensure your Laravel application is in the htdocs\main folder
    pause
    exit /b 1
)

cd /d "%APP_PATH%"

echo Testing application with new PHP version...
"%OLD_XAMPP%\php\php.exe" artisan --version
if errorlevel 1 (
    echo ❌ Laravel application has issues with new PHP version
    echo This might be normal - we'll fix it in the next step
) else (
    echo ✅ Laravel application works with new PHP version
)

echo.
echo [STEP 7] Final Setup
echo ===============================================

echo Now that XAMPP is upgraded, you can run the automated setup:
echo.
echo 1. Run: START_HERE.bat
echo 2. Choose Option 1 (Complete Automated Setup)
echo 3. The automation will handle the rest!
echo.

set /p run_setup="Run the automated setup now? (Y/N): "
if /i "%run_setup%"=="Y" (
    echo.
    echo Starting automated setup...
    call START_HERE.bat
) else (
    echo.
    echo You can run the setup later with: START_HERE.bat
)

echo.
echo ===============================================
echo         XAMPP UPGRADE COMPLETED!
echo ===============================================
echo.
echo ✅ Old XAMPP services stopped
echo ✅ New XAMPP should be installed
echo ✅ PHP version verified: %NEW_PHP_VERSION%
echo ✅ Ready for Laravel application setup
echo.
echo Next: Run START_HERE.bat for complete application setup
echo.

pause
