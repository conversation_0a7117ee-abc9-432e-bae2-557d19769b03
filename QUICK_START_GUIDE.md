# Laravel Botble CMS - Quick Start Guide

## 🚀 After Installing New XAMPP

### Step 1: Run the Automated Setup
```bash
# Navigate to your application directory
cd c:\xampp\htdocs\main

# Run the complete setup automation
check_setup.bat
```

### Step 2: If Setup Needs Manual Intervention
```bash
# Run the CMS installation helper
install_cms.bat
```

### Step 3: If You Encounter Issues
```bash
# Run the troubleshooting tool
troubleshoot.bat
```

## 🌐 Application URLs

| Service | URL | Description |
|---------|-----|-------------|
| **Main Website** | http://localhost/main | Your public website |
| **Admin Panel** | http://localhost/main/admin | CMS administration |
| **PHP Info** | http://localhost/main/phpinfo.php | PHP configuration |

## 🔑 Default Admin Credentials

- **Email:** <EMAIL>
- **Password:** 159357

> ⚠️ **Important:** Change these credentials after first login!

## 📋 Manual Setup Steps (if automation fails)

### 1. Start XAMPP Services
- Open XAMPP Control Panel
- Start **Apache**
- Start **MySQL** (optional, using SQLite by default)

### 2. Install CMS Manually
```bash
cd c:\xampp\htdocs\main
c:\xampp\php\php.exe artisan cms:install
```

### 3. Create Admin User (if needed)
```bash
c:\xampp\php\php.exe artisan make:user <EMAIL> "Admin User" 159357 --role=admin
```

### 4. Clear Caches
```bash
c:\xampp\php\php.exe artisan optimize:clear
c:\xampp\php\php.exe artisan config:cache
```

## 🔧 Common Issues & Solutions

### Issue: "Application not accessible"
**Solution:**
1. Check XAMPP Apache is running
2. Run: `troubleshoot.bat` → Option 6

### Issue: "Database connection failed"
**Solution:**
1. Run: `troubleshoot.bat` → Option 3
2. Or manually create SQLite file: `type nul > database\database.sqlite`

### Issue: "Permission denied errors"
**Solution:**
1. Run XAMPP as Administrator
2. Run: `troubleshoot.bat` → Option 1

### Issue: "500 Internal Server Error"
**Solution:**
1. Run: `troubleshoot.bat` → Option 8 (Complete Repair)
2. Check error logs: `troubleshoot.bat` → Option 7

### Issue: "CMS not installed"
**Solution:**
1. Run: `install_cms.bat`
2. Choose Option 1 for automatic installation

## 📁 Important Files & Directories

```
c:\xampp\htdocs\main\
├── check_setup.bat          # Main setup automation
├── install_cms.bat          # CMS installation helper
├── troubleshoot.bat         # Troubleshooting tool
├── .env                     # Environment configuration
├── database\database.sqlite # SQLite database
├── storage\logs\           # Application logs
└── public\                 # Web accessible files
```

## 🎯 Next Steps After Setup

1. **Visit your website:** http://localhost/main
2. **Login to admin:** http://localhost/main/admin
3. **Change admin password** in admin panel
4. **Configure site settings** in admin panel
5. **Customize your theme** and content

## 📞 Getting Help

If you encounter issues:
1. Run `troubleshoot.bat` first
2. Check `storage\logs\laravel.log` for errors
3. Check `setup_log.txt` for setup details
4. Ensure XAMPP services are running

## 🔄 Updating the Application

To update your Laravel/Botble CMS:
```bash
composer update
php artisan migrate
php artisan cms:publish:assets
php artisan optimize:clear
```

---

**Happy coding! 🎉**
