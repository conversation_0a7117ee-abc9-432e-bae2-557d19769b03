/*!
* inputmask.min.js
* https://github.com/RobinHerbots/Inputmask
* Copyright (c) 2010 - 2018 <PERSON>
* Licensed under the MIT license (http://www.opensource.org/licenses/mit-license.php)
* Version: 4.0.1-beta.3
*/

!function(e){"function"==typeof define&&define.amd?define(["./dependencyLibs/inputmask.dependencyLib","./global/window","./global/document"],e):"object"==typeof exports?module.exports=e(require("./dependencyLibs/inputmask.dependencyLib"),require("./global/window"),require("./global/document")):window.Inputmask=e(window.dependencyLib||jQuery,window,document)}(function(e,t,n,i){var a=navigator.userAgent,r=f("touchstart"),o=/iemobile/i.test(a),s=/iphone/i.test(a)&&!o;function l(t,n,a){if(!(this instanceof l))return new l(t,n,a);this.el=i,this.events={},this.maskset=i,this.refreshValue=!1,!0!==a&&(e.isPlainObject(t)?n=t:(n=n||{},t&&(n.alias=t)),this.opts=e.extend(!0,{},this.defaults,n),this.noMasksCache=n&&n.definitions!==i,this.userOptions=n||{},this.isRTL=this.opts.numericInput,c(this.opts.alias,n,this.opts))}function c(t,n,a){var r=l.prototype.aliases[t];return r?(r.alias&&c(r.alias,i,a),e.extend(!0,a,r),e.extend(!0,a,n),!0):(null===a.mask&&(a.mask=t),!1)}function u(t,n){function a(t,a,r){var o=!1;if(null!==t&&""!==t||((o=null!==r.regex)?t=(t=r.regex).replace(/^(\^)(.*)(\$)$/,"$2"):(o=!0,t=".*")),1===t.length&&!1===r.greedy&&0!==r.repeat&&(r.placeholder=""),r.repeat>0||"*"===r.repeat||"+"===r.repeat){var s="*"===r.repeat?0:"+"===r.repeat?1:r.repeat;t=r.groupmarker[0]+t+r.groupmarker[1]+r.quantifiermarker[0]+s+","+r.repeat+r.quantifiermarker[1]}var c,u=o?"regex_"+r.regex:r.numericInput?t.split("").reverse().join(""):t;return l.prototype.masksCache[u]===i||!0===n?(c={mask:t,maskToken:l.prototype.analyseMask(t,o,r),validPositions:{},_buffer:i,buffer:i,tests:{},excludes:{},metadata:a,maskLength:i},!0!==n&&(l.prototype.masksCache[u]=c,c=e.extend(!0,{},l.prototype.masksCache[u]))):c=e.extend(!0,{},l.prototype.masksCache[u]),c}if(e.isFunction(t.mask)&&(t.mask=t.mask(t)),e.isArray(t.mask)){if(t.mask.length>1){if(null===t.keepStatic){t.keepStatic="auto";for(var r=0;r<t.mask.length;r++)if(t.mask[r].charAt(0)!==t.mask[0].charAt(0)){t.keepStatic=!0;break}}var o=t.groupmarker[0];return e.each(t.isRTL?t.mask.reverse():t.mask,function(n,a){o.length>1&&(o+=t.groupmarker[1]+t.alternatormarker+t.groupmarker[0]),a.mask===i||e.isFunction(a.mask)?o+=a:o+=a.mask}),a(o+=t.groupmarker[1],t.mask,t)}t.mask=t.mask.pop()}return t.mask&&t.mask.mask!==i&&!e.isFunction(t.mask.mask)?a(t.mask.mask,t.mask,t):a(t.mask,t.mask,t)}function f(e){var t=n.createElement("input"),i="on"+e,a=i in t;return a||(t.setAttribute(i,"return;"),a="function"==typeof t[i]),t=null,a}function p(a,c,u){c=c||this.maskset,u=u||this.opts;var h,m,d,v,k=this,g=this.el,b=this.isRTL,y=!1,P=!1,C=!1,E=!1;function x(e,t,n,a,r){!0!==a&&(i,0);var o=u.greedy;r&&(u.greedy=!1),t=t||0;var s,l,c,f=[],p=0,h=w();do{if(!0===e&&A().validPositions[p])l=(c=r&&!0===A().validPositions[p].match.optionality&&A().validPositions[p+1]===i&&(!0===A().validPositions[p].generatedInput||A().validPositions[p].input==u.skipOptionalPartCharacter&&p>0)?j(p,T(p,s,p-1)):A().validPositions[p]).match,s=c.locator.slice(),f.push(!0===n?c.input:!1===n?l.nativeDef:W(p,l));else{l=(c=M(p,s,p-1)).match,s=c.locator.slice();var m=!0!==a&&(!1!==u.jitMasking?u.jitMasking:l.jit);!1===m||m===i||p<h||"number"==typeof m&&isFinite(m)&&m>p?f.push(!1===n?l.nativeDef:W(p,l)):l.jit&&l.optionalQuantifier!==i&&(p,0)}"auto"===u.keepStatic&&l.newBlockMarker&&null!==l.fn&&(u.keepStatic=p-1),p++}while((d===i||p<d)&&(null!==l.fn||""!==l.def)||t>p);return""===f[f.length-1]&&f.pop(),!1===n&&A().maskLength!==i||(A().maskLength=p-1),u.greedy=o,f}function A(){return c}function _(e){var t=A();t.buffer=i,!0!==e&&(t.validPositions={},t.p=0)}function w(e,t,n){var a=-1,r=-1,o=n||A().validPositions;for(var s in e===i&&(e=-1),o){var l=parseInt(s);o[l]&&(t||!0!==o[l].generatedInput)&&(l<=e&&(a=l),l>=e&&(r=l))}return-1===a||a==e?r:-1==r?a:e-a<r-e?a:r}function O(e){var t=e.locator[e.alternation];return"string"==typeof t&&t.length>0&&(t=t.split(",")[0]),t!==i?t.toString():""}function S(e,t){var n=(e.alternation!=i?e.mloc[O(e)]:e.locator).join("");if(""!==n)for(;n.length<t;)n+="0";return n}function j(e,t){for(var n,a,r,o=S(D(e=e>0?e-1:0)),s=0;s<t.length;s++){var l=t[s];n=S(l,o.length);var c=Math.abs(n-o);(a===i||""!==n&&c<a||r&&r.match.optionality&&"master"===r.match.newBlockMarker&&(!l.match.optionality||!l.match.newBlockMarker)||r&&r.match.optionalQuantifier&&!l.match.optionalQuantifier)&&(a=c,r=l)}return r}function M(e,t,n){return A().validPositions[e]||j(e,T(e,t?t.slice():t,n))}function D(e,t){return A().validPositions[e]?A().validPositions[e]:(t||T(e))[0]}function G(e,t){for(var n=!1,i=T(e),a=0;a<i.length;a++)if(i[a].match&&i[a].match.def===t){n=!0;break}return n}function T(t,n,a){var r,o,s,l,c=A().maskToken,f=n?a:0,p=n?n.slice():[0],h=[],m=!1,d=n?n.join(""):"";function v(n,a,o,s){function l(o,s,c){function p(t,n){var i=0===e.inArray(t,n.matches);return i||e.each(n.matches,function(e,a){if(!0===a.isQuantifier?i=p(t,n.matches[e-1]):!0===a.isOptional?i=p(t,a):!0===a.isAlternate&&(i=p(t,a)),i)return!1}),i}function k(t,n,a){var r,o;if((A().tests[t]||A().validPositions[t])&&e.each(A().tests[t]||[A().validPositions[t]],function(e,t){if(t.mloc[n])return r=t,!1;var s=a!==i?a:t.alternation,l=t.locator[s]!==i?t.locator[s].toString().indexOf(n):-1;(o===i||l<o)&&-1!==l&&(r=t,o=l)}),r){var s=r.locator[r.alternation];return(r.mloc[n]||r.mloc[s]||r.locator).slice((a!==i?a:r.alternation)+1)}return a!==i?k(t,n):i}function g(e,t){function n(e){for(var t,n,i=[],a=0,r=e.length;a<r;a++)if("-"===e.charAt(a))for(n=e.charCodeAt(a+1);++t<n;)i.push(String.fromCharCode(t));else t=e.charCodeAt(a),i.push(e.charAt(a));return i.join("")}return u.regex&&null!==e.match.fn&&null!==t.match.fn?-1!==n(t.match.def.replace(/[\[\]]/g,"")).indexOf(n(e.match.def.replace(/[\[\]]/g,""))):e.match.def===t.match.nativeDef}function b(e,t){if(t===i||e.alternation===t.alternation&&-1===e.locator[e.alternation].toString().indexOf(t.locator[t.alternation])){e.mloc=e.mloc||{};var n=e.locator[e.alternation];if(n!==i){if("string"==typeof n&&(n=n.split(",")[0]),e.mloc[n]===i&&(e.mloc[n]=e.locator.slice()),t!==i){for(var a in t.mloc)"string"==typeof a&&(a=a.split(",")[0]),e.mloc[a]===i&&(e.mloc[a]=t.mloc[a]);e.locator[e.alternation]=Object.keys(e.mloc).join(",")}return!0}e.alternation=i}return!1}if(f>5e3)throw"Inputmask: There is probably an error in your mask definition or in the code. Create an issue on github with an example of the mask you are using. "+A().mask;if(f===t&&o.matches===i)return h.push({match:o,locator:s.reverse(),cd:d,mloc:{}}),!0;if(o.matches!==i){if(o.isGroup&&c!==o){if(o=l(n.matches[e.inArray(o,n.matches)+1],s,c))return!0}else if(o.isOptional){var y=o;if(o=v(o,a,s,c)){if(e.each(h,function(e,t){t.match.optionality=!0}),r=h[h.length-1].match,c!==i||!p(r,y))return!0;m=!0,f=t}}else if(o.isAlternator){var P,C=o,E=[],x=h.slice(),_=s.length,w=a.length>0?a.shift():-1;if(-1===w||"string"==typeof w){var O,S=f,j=a.slice(),M=[];if("string"==typeof w)M=w.split(",");else for(O=0;O<C.matches.length;O++)M.push(O.toString());if(A().excludes[t]){for(var D=M.slice(),G=0,T=A().excludes[t].length;G<T;G++)M.splice(M.indexOf(A().excludes[t][G].toString()),1);0===M.length&&(A().excludes[t]=i,M=D)}(!0===u.keepStatic||isFinite(parseInt(u.keepStatic))&&S>=u.keepStatic)&&(M=M.slice(0,1));for(var B=!1,L=0;L<M.length;L++){O=parseInt(M[L]),h=[],a="string"==typeof w&&k(f,O,_)||j.slice(),C.matches[O]&&l(C.matches[O],[O].concat(s),c)?o=!0:0===L&&(B=!0),P=h.slice(),f=S,h=[];for(var I=0;I<P.length;I++){var F=P[I],N=!1;F.match.jit=F.match.jit||B,F.alternation=F.alternation||_,b(F);for(var R=0;R<E.length;R++){var V=E[R];if("string"!=typeof w||F.alternation!==i&&-1!==e.inArray(F.locator[F.alternation].toString(),M)){if(F.match.nativeDef===V.match.nativeDef){N=!0,b(V,F);break}if(g(F,V)){b(F,V)&&(N=!0,E.splice(E.indexOf(V),0,F));break}if(g(V,F)){b(V,F);break}if(U=V,null===(Q=F).match.fn&&null!==U.match.fn&&U.match.fn.test(Q.match.def,A(),t,!1,u,!1)){b(F,V)&&(N=!0,E.splice(E.indexOf(V),0,F));break}}}N||E.push(F)}}h=x.concat(E),f=t,m=h.length>0,o=E.length>0,a=j.slice()}else o=l(C.matches[w]||n.matches[w],[w].concat(s),c);if(o)return!0}else if(o.isQuantifier&&c!==n.matches[e.inArray(o,n.matches)-1])for(var K=o,H=a.length>0?a.shift():0;H<(isNaN(K.quantifier.max)?H+1:K.quantifier.max)&&f<=t;H++){var q=n.matches[e.inArray(K,n.matches)-1];if(o=l(q,[H].concat(s),q)){if((r=h[h.length-1].match).optionalQuantifier=H>K.quantifier.min-1,r.jit=H+q.matches.indexOf(r)>=K.quantifier.jit,p(r,q)&&H>K.quantifier.min-1){m=!0,f=t;break}if(K.quantifier.jit!==i&&isNaN(K.quantifier.max)&&r.optionalQuantifier&&A().validPositions[t-1]===i){h.pop(),m=!0,f=t,d=i;break}return!0}}else if(o=v(o,a,s,c))return!0}else f++;var Q,U}for(var c=a.length>0?a.shift():0;c<n.matches.length;c++)if(!0!==n.matches[c].isQuantifier){var p=l(n.matches[c],[c].concat(o),s);if(p&&f===t)return p;if(f>t)break}}if(t>-1){if(n===i){for(var k,g=t-1;(k=A().validPositions[g]||A().tests[g])===i&&g>-1;)g--;k!==i&&g>-1&&(o=g,s=k,l=[],e.isArray(s)||(s=[s]),s.length>0&&(s[0].alternation===i?0===(l=j(o,s.slice()).locator.slice()).length&&(l=s[0].locator.slice()):e.each(s,function(e,t){if(""!==t.def)if(0===l.length)l=t.locator.slice();else for(var n=0;n<l.length;n++)t.locator[n]&&-1===l[n].toString().indexOf(t.locator[n])&&(l[n]+=","+t.locator[n])})),d=(p=l).join(""),f=g)}if(A().tests[t]&&A().tests[t][0].cd===d)return A().tests[t];for(var b=p.shift();b<c.length;b++){if(v(c[b],p,[b])&&f===t||f>t)break}}return(0===h.length||m)&&h.push({match:{fn:null,optionality:!1,casing:null,def:"",placeholder:""},locator:[],mloc:{},cd:d}),n!==i&&A().tests[t]?e.extend(!0,[],h):(A().tests[t]=e.extend(!0,[],h),A().tests[t])}function B(){return A()._buffer===i&&(A()._buffer=x(!1,1),A().buffer===i&&(A().buffer=A()._buffer.slice())),A()._buffer}function L(e){return A().buffer!==i&&!0!==e||(A().buffer=x(!0,w(),!0)),A().buffer}function I(e,t,n){var a,r;if(!0===e)_(),e=0,t=n.length;else for(a=e;a<t;a++)delete A().validPositions[a];for(r=e,a=e;a<t;a++)if(_(!0),n[a]!==u.skipOptionalPartCharacter){var o=R(r,n[a],!0,!0);!1!==o&&(_(!0),r=o.caret!==i?o.caret:o.pos+1)}}function F(t,n,a){for(var r,o=u.greedy?n:n.slice(0,1),s=!1,l=a!==i?a.split(","):[],c=0;c<l.length;c++)-1!==(r=t.indexOf(l[c]))&&t.splice(r,1);for(var f=0;f<t.length;f++)if(-1!==e.inArray(t[f],o)){s=!0;break}return s}function N(t,n,a,r,o){var s,l,c,u,f,p,h,m=e.extend(!0,{},A().validPositions),d=!1,v=o!==i?o:w();if(-1===v&&o===i)l=(u=D(s=0)).alternation;else for(;v>=0;v--)if((c=A().validPositions[v])&&c.alternation!==i){if(u&&u.locator[c.alternation]!==c.locator[c.alternation])break;s=v,l=A().validPositions[s].alternation,u=c}if(l!==i){h=parseInt(s),A().excludes[h]=A().excludes[h]||[],!0!==t&&A().excludes[h].push(O(u));var k=[],g=0;for(f=h;f<w(i,!0)+1;f++)(p=A().validPositions[f])&&!0!==p.generatedInput?k.push(p.input):f<t&&g++,delete A().validPositions[f];for(;A().excludes[h]&&A().excludes[h].length<10;){var b=-1*g,y=k.slice();for(A().tests[h]=i,_(!0),d=!0;y.length>0;){var P=y.shift();if(!(d=R(w(i,!0)+1,P,!1,r,!0)))break}if(d&&n!==i){var C=w(t)+1;for(f=h;f<w()+1;f++)((p=A().validPositions[f])===i||null==p.match.fn)&&f<t+b&&b++;d=R((t+=b)>C?C:t,n,a,r,!0)}if(d)break;if(_(),u=D(h),A().validPositions=e.extend(!0,{},m),!A().excludes[h]){d=N(t,n,a,r,h-1);break}var E=O(u);if(-1!==A().excludes[h].indexOf(E)){d=N(t,n,a,r,h-1);break}for(A().excludes[h].push(E),f=h;f<w(i,!0)+1;f++)delete A().validPositions[f]}}return A().excludes[h]=i,d}function R(t,n,a,r,o,s){function c(e){return b?e.begin-e.end>1||e.begin-e.end==1:e.end-e.begin>1||e.end-e.begin==1}a=!0===a;var f=t;function p(n,a,o){var s=!1;return e.each(T(n),function(f,p){var h=p.match;if(L(!0),!1!==(s=null!=h.fn?h.fn.test(a,A(),n,o,u,c(t)):(a===h.def||a===u.skipOptionalPartCharacter)&&""!==h.def&&{c:W(n,h,!0)||h.def,pos:n})){var m=s.c!==i?s.c:a,d=n;return m=m===u.skipOptionalPartCharacter&&null===h.fn?W(n,h,!0)||h.def:m,s.remove!==i&&(e.isArray(s.remove)||(s.remove=[s.remove]),e.each(s.remove.sort(function(e,t){return t-e}),function(e,t){K({begin:t,end:t+1})})),s.insert!==i&&(e.isArray(s.insert)||(s.insert=[s.insert]),e.each(s.insert.sort(function(e,t){return e-t}),function(e,t){R(t.pos,t.c,!0,r)})),!0!==s&&s.pos!==i&&s.pos!==n&&(d=s.pos),!0!==s&&s.pos===i&&s.c===i?!1:(K(t,e.extend({},p,{input:function(t,n,i){switch(u.casing||n.casing){case"upper":t=t.toUpperCase();break;case"lower":t=t.toLowerCase();break;case"title":var a=A().validPositions[i-1];t=0===i||a&&a.input===String.fromCharCode(l.keyCode.SPACE)?t.toUpperCase():t.toLowerCase();break;default:if(e.isFunction(u.casing)){var r=Array.prototype.slice.call(arguments);r.push(A().validPositions),t=u.casing.apply(this,r)}}return t}(m,h,d)}),r,d)||(s=!1),!1)}}),s}t.begin!==i&&(f=b?t.end:t.begin);var h=!0,m=e.extend(!0,{},A().validPositions);if(e.isFunction(u.preValidation)&&!a&&!0!==r&&!0!==s&&(h=u.preValidation(L(),f,n,c(t),u,A())),!0===h){if(V(i,f,!0),(d===i||f<d)&&(h=p(f,n,a),(!a||!0===r)&&!1===h&&!0!==s)){var v=A().validPositions[f];if(!v||null!==v.match.fn||v.match.def!==n&&n!==u.skipOptionalPartCharacter){if((u.insertMode||A().validPositions[q(f)]===i)&&!H(f,!0))for(var k=f+1,g=q(f);k<=g;k++)if(!1!==(h=p(k,n,a))){h=V(f,h.pos!==i?h.pos:k)||h,f=k;break}}else h={caret:q(f)}}!1!==h||!1===u.keepStatic||null!=u.regex&&!ie(L())||a||!0===o||(h=N(f,n,a,r)),!0===h&&(h={pos:f})}if(e.isFunction(u.postValidation)&&!1!==h&&!a&&!0!==r&&!0!==s){var y=u.postValidation(L(!0),h,u);if(y!==i){if(y.refreshFromBuffer&&y.buffer){var P=y.refreshFromBuffer;I(!0===P?P:P.start,P.end,y.buffer)}h=!0===y?h:y}}return h&&h.pos===i&&(h.pos=f),!1!==h&&!0!==s||(_(!0),A().validPositions=e.extend(!0,{},m)),h}function V(t,n,a){var r;if(t===i)for(t=n-1;t>0&&!A().validPositions[t];t--);for(var o=t;o<n;o++){if(A().validPositions[o]===i&&!H(o,!0))if(0==o?D(o):A().validPositions[o-1]){var s=T(o).slice();""===s[s.length-1].match.def&&s.pop();var l=j(o,s);if((l=e.extend({},l,{input:W(o,l.match,!0)||l.match.def})).generatedInput=!0,K(o,l,!0),!0!==a){var c=A().validPositions[n].input;A().validPositions[n]=i,r=R(n,c,!0,!0)}}}return r}function K(t,n,a,r){function o(e,t,n){var a=t[e];if(a!==i&&(null===a.match.fn&&!0!==a.match.optionality||a.input===u.radixPoint)){var r=n.begin<=e-1?t[e-1]&&null===t[e-1].match.fn&&t[e-1]:t[e-1],o=n.end>e+1?t[e+1]&&null===t[e+1].match.fn&&t[e+1]:t[e+1];return r&&o}return!1}var s=t.begin!==i?t.begin:t,l=t.end!==i?t.end:t;if(t.begin>t.end&&(s=t.end,l=t.begin),r=r!==i?r:s,s!==l||u.insertMode&&A().validPositions[r]!==i&&a===i){var c=e.extend(!0,{},A().validPositions),f=w(i,!0);for(A().p=s,v=f;v>=s;v--)A().validPositions[v]&&"+"===A().validPositions[v].match.nativeDef&&(u.isNegative=!1),delete A().validPositions[v];var p=!0,h=r,m=(A().validPositions,!1),d=h,v=h;for(n&&(A().validPositions[r]=e.extend(!0,{},n),d++,h++,s<l&&v++);v<=f;v++){var k=c[v];if(k!==i&&(v>=l||v>=s&&!0!==k.generatedInput&&o(v,c,{begin:s,end:l}))){for(;""!==D(d).match.def;){if(!1===m&&c[d]&&c[d].match.nativeDef===k.match.nativeDef)A().validPositions[d]=e.extend(!0,{},c[d]),A().validPositions[d].input=k.input,V(i,d,!0),h=d+1,p=!0;else if(G(d,k.match.def)){var g=R(d,k.input,!0,!0);p=!1!==g,h=g.caret||g.insert?w():d+1,m=!0}else if(!(p=!0===k.generatedInput||k.input===u.radixPoint&&!0===u.numericInput)&&""===D(d).match.def)break;if(p)break;d++}""==D(d).match.def&&(p=!1),d=h}if(!p)break}if(!p)return A().validPositions=e.extend(!0,{},c),_(!0),!1}else n&&(A().validPositions[r]=e.extend(!0,{},n));return _(!0),!0}function H(e,t){var n=M(e).match;if(""===n.def&&(n=D(e).match),null!=n.fn)return n.fn;if(!0!==t&&e>-1){var i=T(e);return i.length>1+(""===i[i.length-1].match.def?1:0)}return!1}function q(e,t){for(var n=e+1;""!==D(n).match.def&&(!0===t&&(!0!==D(n).match.newBlockMarker||!H(n))||!0!==t&&!H(n));)n++;return n}function Q(e,t){var n,i=e;if(i<=0)return 0;for(;--i>0&&(!0===t&&!0!==D(i).match.newBlockMarker||!0!==t&&!H(i)&&((n=T(i)).length<2||2===n.length&&""===n[1].match.def)););return i}function U(t,n,a,r,o){if(r&&e.isFunction(u.onBeforeWrite)){var s=u.onBeforeWrite.call(k,r,n,a,u);if(s){if(s.refreshFromBuffer){var l=s.refreshFromBuffer;I(!0===l?l:l.start,l.end,s.buffer||n),n=L(!0)}a!==i&&(a=s.caret!==i?s.caret:a)}}if(t!==i&&(t.inputmask._valueSet(n.join("")),a===i||r!==i&&"blur"===r.type?oe(t,a,0===n.length):ee(t,a),!0===o)){var c=e(t),f=t.inputmask._valueGet();P=!0,c.trigger("input"),setTimeout(function(){f===B().join("")?c.trigger("cleared"):!0===ie(n)&&c.trigger("complete")},0)}}function W(t,n,a){if((n=n||D(t).match).placeholder!==i||!0===a)return e.isFunction(n.placeholder)?n.placeholder(u):n.placeholder;if(null===n.fn){if(t>-1&&A().validPositions[t]===i){var r,o=T(t),s=[];if(o.length>1+(""===o[o.length-1].match.def?1:0))for(var l=0;l<o.length;l++)if(!0!==o[l].match.optionality&&!0!==o[l].match.optionalQuantifier&&(null===o[l].match.fn||r===i||!1!==o[l].match.fn.test(r.match.def,A(),t,!0,u))&&(s.push(o[l]),null===o[l].match.fn&&(r=o[l]),s.length>1&&/[0-9a-bA-Z]/.test(s[0].match.def)))return u.placeholder.charAt(t%u.placeholder.length)}return n.def}return u.placeholder.charAt(t%u.placeholder.length)}var $,z={on:function(t,n,a){var c=function(t){var n=this;if(n.inputmask===i&&"FORM"!==this.nodeName){var c=e.data(n,"_inputmask_opts");c?new l(c).mask(n):z.off(n)}else{if("setvalue"===t.type||"FORM"===this.nodeName||!(n.disabled||n.readOnly&&!("keydown"===t.type&&t.ctrlKey&&67===t.keyCode||!1===u.tabThrough&&t.keyCode===l.keyCode.TAB))){switch(t.type){case"input":if(!0===P)return P=!1,t.preventDefault();if(r){var f=arguments;return setTimeout(function(){a.apply(n,f),ee(n,n.inputmask.caretPos,i,!0)},0),!1}break;case"keydown":y=!1,P=!1;break;case"keypress":if(!0===y)return t.preventDefault();y=!0;break;case"click":if(o||s){f=arguments;return setTimeout(function(){a.apply(n,f)},0),!1}}var p=a.apply(n,arguments);return!1===p&&(t.preventDefault(),t.stopPropagation()),p}t.preventDefault()}};t.inputmask.events[n]=t.inputmask.events[n]||[],t.inputmask.events[n].push(c),-1!==e.inArray(n,["submit","reset"])?null!==t.form&&e(t.form).on(n,c):e(t).on(n,c)},off:function(t,n){var i;t.inputmask&&t.inputmask.events&&(n?(i=[])[n]=t.inputmask.events[n]:i=t.inputmask.events,e.each(i,function(n,i){for(;i.length>0;){var a=i.pop();-1!==e.inArray(n,["submit","reset"])?null!==t.form&&e(t.form).off(n,a):e(t).off(n,a)}delete t.inputmask.events[n]}))}},X={keydownEvent:function(t){var n=this,i=e(n),a=t.keyCode,r=ee(n);if(a===l.keyCode.BACKSPACE||a===l.keyCode.DELETE||s&&a===l.keyCode.BACKSPACE_SAFARI||t.ctrlKey&&a===l.keyCode.X&&!f("cut"))t.preventDefault(),ae(n,a,r),U(n,L(!0),A().p,t,n.inputmask._valueGet()!==L().join(""));else if(a===l.keyCode.END||a===l.keyCode.PAGE_DOWN){t.preventDefault();var o=q(w());ee(n,t.shiftKey?r.begin:o,o,!0)}else a===l.keyCode.HOME&&!t.shiftKey||a===l.keyCode.PAGE_UP?(t.preventDefault(),ee(n,0,t.shiftKey?r.begin:0,!0)):(u.undoOnEscape&&a===l.keyCode.ESCAPE||90===a&&t.ctrlKey)&&!0!==t.altKey?(Z(n,!0,!1,h.split("")),i.trigger("click")):a!==l.keyCode.INSERT||t.shiftKey||t.ctrlKey?!0===u.tabThrough&&a===l.keyCode.TAB&&(!0===t.shiftKey?(null===D(r.begin).match.fn&&(r.begin=q(r.begin)),r.end=Q(r.begin,!0),r.begin=Q(r.end,!0)):(r.begin=q(r.begin,!0),r.end=q(r.begin,!0),r.end<A().maskLength&&r.end--),r.begin<A().maskLength&&(t.preventDefault(),ee(n,r.begin,r.end))):(u.insertMode=!u.insertMode,n.setAttribute("im-insert",u.insertMode));u.onKeyDown.call(this,t,L(),ee(n).begin,u),C=-1!==e.inArray(a,u.ignorables)},keypressEvent:function(t,n,a,r,o){var s=this,c=e(s),f=t.which||t.charCode||t.keyCode;if(!(!0===n||t.ctrlKey&&t.altKey)&&(t.ctrlKey||t.metaKey||C))return f===l.keyCode.ENTER&&h!==L().join("")&&(h=L().join(""),setTimeout(function(){c.trigger("change")},0)),!0;if(f){46===f&&!1===t.shiftKey&&""!==u.radixPoint&&(f=u.radixPoint.charCodeAt(0));var p,m=n?{begin:o,end:o}:ee(s),d=String.fromCharCode(f),v=0;if(u._radixDance&&u.numericInput){var k=L().indexOf(u.radixPoint.charAt(0))+1;m.begin<=k&&(f===u.radixPoint.charCodeAt(0)&&(v=1),m.begin-=1,m.end-=1)}A().writeOutBuffer=!0;var g=R(m,d,r);if(!1!==g&&(_(!0),p=g.caret!==i?g.caret:q(g.pos.begin?g.pos.begin:g.pos),A().p=p),p=(u.numericInput&&g.caret===i?Q(p):p)+v,!1!==a&&(setTimeout(function(){u.onKeyValidation.call(s,f,g,u)},0),A().writeOutBuffer&&!1!==g)){var b=L();U(s,b,p,t,!0!==n)}if(t.preventDefault(),n)return!1!==g&&(g.forwardPosition=p),g}},pasteEvent:function(n){var i,a=this,r=n.originalEvent||n,o=(e(a),a.inputmask._valueGet(!0)),s=ee(a);b&&(i=s.end,s.end=s.begin,s.begin=i);var l=o.substr(0,s.begin),c=o.substr(s.end,o.length);if(l===(b?B().reverse():B()).slice(0,s.begin).join("")&&(l=""),c===(b?B().reverse():B()).slice(s.end).join("")&&(c=""),t.clipboardData&&t.clipboardData.getData)o=l+t.clipboardData.getData("Text")+c;else{if(!r.clipboardData||!r.clipboardData.getData)return!0;o=l+r.clipboardData.getData("text/plain")+c}var f=o;if(e.isFunction(u.onBeforePaste)){if(!1===(f=u.onBeforePaste.call(k,o,u)))return n.preventDefault();f||(f=o)}return Z(a,!1,!1,f.toString().split("")),U(a,L(),q(w()),n,h!==L().join("")),n.preventDefault()},inputFallBackEvent:function(t){var n,i,a=this,r=a.inputmask._valueGet();if(L().join("")!==r){var s=ee(a);if(i=s,"."===(n=r).charAt(i.begin-1)&&""!==u.radixPoint&&((n=n.split(""))[i.begin-1]=u.radixPoint.charAt(0),n=n.join("")),r=function(e,t,n){if(o){var i=t.replace(L().join(""),"");if(1===i.length){var a=t.split("");a.splice(n.begin,0,i),t=a.join("")}}return t}(0,r=n,s),L().join("")!==r){var c=L().join(""),f=!u.numericInput&&r.length>c.length?-1:0,p=r.substr(0,s.begin),h=r.substr(s.begin),m=c.substr(0,s.begin+f),d=c.substr(s.begin+f),v=s,k="",g=!1;if(p!==m){for(var b=(g=p.length>=m.length)?p.length:m.length,y=0;p.charAt(y)===m.charAt(y)&&y<b;y++);g&&(0===f&&(v.begin=y),k+=p.slice(y,v.end))}if(h!==d&&(h.length>d.length?k+=h.slice(0,1):h.length<d.length&&(v.end+=d.length-h.length,g||""===u.radixPoint||""!==h||p.charAt(v.begin+f-1)!==u.radixPoint||(v.begin--,k=u.radixPoint))),U(a,L(),{begin:v.begin+f,end:v.end+f}),k.length>0)e.each(k.split(""),function(t,n){var i=new e.Event("keypress");i.which=n.charCodeAt(0),C=!1,X.keypressEvent.call(a,i)});else{v.begin===v.end-1&&(v.begin=Q(v.begin+1),v.begin===v.end-1?ee(a,v.begin):ee(a,v.begin,v.end));var P=new e.Event("keydown");P.keyCode=u.numericInput?l.keyCode.BACKSPACE:l.keyCode.DELETE,X.keydownEvent.call(a,P)}t.preventDefault()}}},beforeInputEvent:function(t){if(t.cancelable){var n=this;switch(t.inputType){case"insertText":return e.each(t.data.split(""),function(t,i){var a=new e.Event("keypress");a.which=i.charCodeAt(0),C=!1,X.keypressEvent.call(n,a)}),t.preventDefault();case"deleteContentBackward":return(i=new e.Event("keydown")).keyCode=l.keyCode.BACKSPACE,X.keydownEvent.call(n,i),t.preventDefault();case"deleteContentForward":var i;return(i=new e.Event("keydown")).keyCode=l.keyCode.DELETE,X.keydownEvent.call(n,i),t.preventDefault()}}},setValueEvent:function(t){this.inputmask.refreshValue=!1;var n=(n=t&&t.detail?t.detail[0]:arguments[1])||this.inputmask._valueGet(!0);e.isFunction(u.onBeforeMask)&&(n=u.onBeforeMask.call(k,n,u)||n),Z(this,!0,!1,n=n.split("")),h=L().join(""),(u.clearMaskOnLostFocus||u.clearIncomplete)&&this.inputmask._valueGet()===B().join("")&&this.inputmask._valueSet("")},focusEvent:function(e){var t=this,n=t.inputmask._valueGet();u.showMaskOnFocus&&(!u.showMaskOnHover||u.showMaskOnHover&&""===n)&&(t.inputmask._valueGet()!==L().join("")?U(t,L(),q(w())):!1===E&&ee(t,q(w()))),!0===u.positionCaretOnTab&&!1===E&&X.clickEvent.apply(t,[e,!0]),h=L().join("")},mouseleaveEvent:function(e){if(E=!1,u.clearMaskOnLostFocus&&n.activeElement!==this){var t=L().slice(),i=this.inputmask._valueGet();i!==this.getAttribute("placeholder")&&""!==i&&(-1===w()&&i===B().join("")?t=[]:ne(t),U(this,t))}},clickEvent:function(t,a){var r=this;setTimeout(function(){if(n.activeElement===r){var t=ee(r);if(a&&(b?t.end=t.begin:t.begin=t.end),t.begin===t.end)switch(u.positionCaretOnClick){case"none":break;case"select":ee(r,0,L().length);break;case"ignore":ee(r,q(w()));break;case"radixFocus":if(function(t){if(""!==u.radixPoint){var n=A().validPositions;if(n[t]===i||n[t].input===W(t)){if(t<q(-1))return!0;var a=e.inArray(u.radixPoint,L());if(-1!==a){for(var r in n)if(a<r&&n[r].input!==W(r))return!1;return!0}}}return!1}(t.begin)){var o=L().join("").indexOf(u.radixPoint);ee(r,u.numericInput?q(o):o);break}default:var s=t.begin,l=w(s,!0),c=q(l);if(s<c)ee(r,H(s,!0)||H(s-1,!0)?s:q(s));else{var f=A().validPositions[l],p=M(c,f?f.match.locator:i,f),h=W(c,p.match);if(""!==h&&L()[c]!==h&&!0!==p.match.optionalQuantifier&&!0!==p.match.newBlockMarker||!H(c,u.keepStatic)&&p.match.def===h){var m=q(c);(s>=m||s===c)&&(c=m)}ee(r,c)}}}},0)},cutEvent:function(i){e(this);var a=ee(this),r=i.originalEvent||i,o=t.clipboardData||r.clipboardData,s=b?L().slice(a.end,a.begin):L().slice(a.begin,a.end);o.setData("text",b?s.reverse().join(""):s.join("")),n.execCommand&&n.execCommand("copy"),ae(this,l.keyCode.DELETE,a),U(this,L(),A().p,i,h!==L().join(""))},blurEvent:function(t){var n=e(this);if(this.inputmask){var a=this.inputmask._valueGet(),r=L().slice();""===a&&v===i||(u.clearMaskOnLostFocus&&(-1===w()&&a===B().join("")?r=[]:ne(r)),!1===ie(r)&&(setTimeout(function(){n.trigger("incomplete")},0),u.clearIncomplete&&(_(),r=u.clearMaskOnLostFocus?[]:B().slice())),U(this,r,i,t)),h!==L().join("")&&(h=r.join(""),n.trigger("change"))}},mouseenterEvent:function(e){E=!0,n.activeElement!==this&&u.showMaskOnHover&&this.inputmask._valueGet()!==L().join("")&&U(this,L())},submitEvent:function(e){h!==L().join("")&&m.trigger("change"),u.clearMaskOnLostFocus&&-1===w()&&g.inputmask._valueGet&&g.inputmask._valueGet()===B().join("")&&g.inputmask._valueSet(""),u.clearIncomplete&&!1===ie(L())&&g.inputmask._valueSet(""),u.removeMaskOnSubmit&&(g.inputmask._valueSet(g.inputmask.unmaskedvalue(),!0),setTimeout(function(){U(g,L())},0))},resetEvent:function(e){g.inputmask.refreshValue=!0,setTimeout(function(){m.trigger("setvalue")},0)}};function Z(t,n,a,r,o){var s=this||t.inputmask,c=r.slice(),f="",p=-1,h=i;if(_(),a||!0===u.autoUnmask)p=q(p);else{var m=B().slice(0,q(-1)).join(""),d=c.join("").match(new RegExp("^"+l.escapeRegex(m),"g"));d&&d.length>0&&(c.splice(0,d.length*m.length),p=q(p))}-1===p?(A().p=q(p),p=0):A().p=p,s.caretPos={begin:p},e.each(c,function(n,r){if(r!==i)if(A().validPositions[n]===i&&c[n]===W(n)&&H(n,!0)&&!1===R(n,c[n],!0,i,i,!0))A().p++;else{var o=new e.Event("_checkval");o.which=r.charCodeAt(0),f+=r;var l=w(i,!0);u=p,m=f,-1===x(!0,0,!1).slice(u,q(u)).join("").replace(/'/g,"").indexOf(m)||H(u)||!(D(u).match.nativeDef===m.charAt(0)||null===D(u).match.fn&&D(u).match.nativeDef==="'"+m.charAt(0)||" "===D(u).match.nativeDef&&(D(u+1).match.nativeDef===m.charAt(0)||null===D(u+1).match.fn&&D(u+1).match.nativeDef==="'"+m.charAt(0)))?(h=X.keypressEvent.call(t,o,!0,!1,a,s.caretPos.begin))&&(p=s.caretPos.begin+1,f=""):h=X.keypressEvent.call(t,o,!0,!1,a,l+1),h&&(U(i,L(),h.forwardPosition,o,!1),s.caretPos={begin:h.forwardPosition,end:h.forwardPosition})}var u,m}),n&&U(t,L(),h?h.forwardPosition:i,o||new e.Event("checkval"),o&&"input"===o.type)}function J(t){if(t){if(t.inputmask===i)return t.value;t.inputmask&&t.inputmask.refreshValue&&X.setValueEvent.call(t)}var n=[],a=A().validPositions;for(var r in a)a[r].match&&null!=a[r].match.fn&&n.push(a[r].input);var o=0===n.length?"":(b?n.reverse():n).join("");if(e.isFunction(u.onUnMask)){var s=(b?L().slice().reverse():L()).join("");o=u.onUnMask.call(k,s,o,u)}return o}function Y(e){return!b||"number"!=typeof e||u.greedy&&""===u.placeholder||!g||(e=g.inputmask._valueGet().length-e),e}function ee(a,r,o,s){var l;if(r===i)return"selectionStart"in a?(r=a.selectionStart,o=a.selectionEnd):t.getSelection?(l=t.getSelection().getRangeAt(0)).commonAncestorContainer.parentNode!==a&&l.commonAncestorContainer!==a||(r=l.startOffset,o=l.endOffset):n.selection&&n.selection.createRange&&(o=(r=0-(l=n.selection.createRange()).duplicate().moveStart("character",-a.inputmask._valueGet().length))+l.text.length),{begin:s?r:Y(r),end:s?o:Y(o)};if(e.isArray(r)&&(o=b?r[0]:r[1],r=b?r[1]:r[0]),r.begin!==i&&(o=b?r.begin:r.end,r=b?r.end:r.begin),"number"==typeof r){r=s?r:Y(r),o="number"==typeof(o=s?o:Y(o))?o:r;var c=parseInt(((a.ownerDocument.defaultView||t).getComputedStyle?(a.ownerDocument.defaultView||t).getComputedStyle(a,null):a.currentStyle).fontSize)*o;if(a.scrollLeft=c>a.scrollWidth?c:0,a.inputmask.caretPos={begin:r,end:o},"selectionStart"in a)a.selectionStart=r,a.selectionEnd=o;else if(t.getSelection){if(l=n.createRange(),a.firstChild===i||null===a.firstChild){var u=n.createTextNode("");a.appendChild(u)}l.setStart(a.firstChild,r<a.inputmask._valueGet().length?r:a.inputmask._valueGet().length),l.setEnd(a.firstChild,o<a.inputmask._valueGet().length?o:a.inputmask._valueGet().length),l.collapse(!0);var f=t.getSelection();f.removeAllRanges(),f.addRange(l)}else a.createTextRange&&((l=a.createTextRange()).collapse(!0),l.moveEnd("character",o),l.moveStart("character",r),l.select());oe(a,{begin:r,end:o})}}function te(t){var n,a,r=x(!0,w(),!0,!0),o=r.length,s=w(),l={},c=A().validPositions[s],u=c!==i?c.locator.slice():i;for(n=s+1;n<r.length;n++)u=(a=M(n,u,n-1)).locator.slice(),l[n]=e.extend(!0,{},a);var f=c&&c.alternation!==i?c.locator[c.alternation]:i;for(n=o-1;n>s&&(((a=l[n]).match.optionality||a.match.optionalQuantifier&&a.match.newBlockMarker||f&&(f!==l[n].locator[c.alternation]&&null!=a.match.fn||null===a.match.fn&&a.locator[c.alternation]&&F(a.locator[c.alternation].toString().split(","),f.toString().split(","))&&""!==T(n)[0].def))&&r[n]===W(n,a.match));n--)o--;return t?{l:o,def:l[o]?l[o].match:i}:o}function ne(e){e.length=0;for(var t,n=x(!0,0,!0,i,!0);(t=n.shift())!==i;)e.push(t);return e}function ie(t){if(e.isFunction(u.isComplete))return u.isComplete(t,u);if("*"===u.repeat)return i;var n=!1,a=te(!0),r=Q(a.l);if(a.def===i||a.def.newBlockMarker||a.def.optionality||a.def.optionalQuantifier){n=!0;for(var o=0;o<=r;o++){var s=M(o).match;if(null!==s.fn&&A().validPositions[o]===i&&!0!==s.optionality&&!0!==s.optionalQuantifier||null===s.fn&&t[o]!==W(o,s)){n=!1;break}}}return n}function ae(e,t,n,a,r){if((u.numericInput||b)&&(t===l.keyCode.BACKSPACE?t=l.keyCode.DELETE:t===l.keyCode.DELETE&&(t=l.keyCode.BACKSPACE),b)){var o=n.end;n.end=n.begin,n.begin=o}if(t===l.keyCode.BACKSPACE&&n.end-n.begin<1?(n.begin=Q(n.begin),A().validPositions[n.begin]!==i&&A().validPositions[n.begin].input===u.groupSeparator&&n.begin--):t===l.keyCode.DELETE&&n.begin===n.end&&(n.end=H(n.end,!0)&&A().validPositions[n.end]&&A().validPositions[n.end].input!==u.radixPoint?n.end+1:q(n.end)+1,A().validPositions[n.begin]!==i&&A().validPositions[n.begin].input===u.groupSeparator&&n.end++),K(n),!0!==a&&!1!==u.keepStatic||null!==u.regex){var s=N(!0);if(s){var c=s.caret!==i?s.caret:s.pos?q(s.pos.begin?s.pos.begin:s.pos):w(-1,!0);(t!==l.keyCode.DELETE||n.begin>c)&&n.begin}}var f=w(n.begin,!0);if(f<n.begin||-1===n.begin)A().p=q(f);else if(!0!==a&&(A().p=n.begin,!0!==r))for(;A().p<f&&A().validPositions[A().p]===i;)A().p++}function re(i){var a=(i.ownerDocument.defaultView||t).getComputedStyle(i,null);var r=n.createElement("div");r.style.width=a.width,r.style.textAlign=a.textAlign,v=n.createElement("div"),i.inputmask.colorMask=v,v.className="im-colormask",i.parentNode.insertBefore(v,i),i.parentNode.removeChild(i),v.appendChild(i),v.appendChild(r),i.style.left=r.offsetLeft+"px",e(v).on("mouseleave",function(e){return X.mouseleaveEvent.call(i,[e])}),e(v).on("mouseenter",function(e){return X.mouseenterEvent.call(i,[e])}),e(v).on("click",function(e){return ee(i,function(e){var t,r=n.createElement("span");for(var o in a)isNaN(o)&&-1!==o.indexOf("font")&&(r.style[o]=a[o]);r.style.textTransform=a.textTransform,r.style.letterSpacing=a.letterSpacing,r.style.position="absolute",r.style.height="auto",r.style.width="auto",r.style.visibility="hidden",r.style.whiteSpace="nowrap",n.body.appendChild(r);var s,l=i.inputmask._valueGet(),c=0;for(t=0,s=l.length;t<=s;t++){if(r.innerHTML+=l.charAt(t)||"_",r.offsetWidth>=e){var u=e-c,f=r.offsetWidth-e;r.innerHTML=l.charAt(t),t=(u-=r.offsetWidth/3)<f?t-1:t;break}c=r.offsetWidth}return n.body.removeChild(r),t}(e.clientX)),X.clickEvent.call(i,[e])})}function oe(e,t,a){var r,o,s,l=[],c=!1,f=0;function p(e){if(e===i&&(e=""),c||null!==r.fn&&o.input!==i)if(c&&(null!==r.fn&&o.input!==i||""===r.def)){c=!1;var t=l.length;l[t-1]=l[t-1]+"</span>",l.push(e)}else l.push(e);else c=!0,l.push("<span class='im-static'>"+e)}if(v!==i){var h=L();if(t===i?t=ee(e):t.begin===i&&(t={begin:t,end:t}),!0!==a){var m=w();do{A().validPositions[f]?(o=A().validPositions[f],r=o.match,s=o.locator.slice(),p(h[f])):(o=M(f,s,f-1),r=o.match,s=o.locator.slice(),!1===u.jitMasking||f<m||"number"==typeof u.jitMasking&&isFinite(u.jitMasking)&&u.jitMasking>f?p(W(f,r)):c=!1),f++}while((d===i||f<d)&&(null!==r.fn||""!==r.def)||m>f||c);c&&p(),n.activeElement===e&&(l.splice(t.begin,0,t.begin===t.end||t.end>A().maskLength?'<mark class="im-caret" style="border-right-width: 1px;border-right-style: solid;">':'<mark class="im-caret-select">'),l.splice(t.end+1,0,"</mark>"))}var k=v.getElementsByTagName("div")[0];k.innerHTML=l.join(""),e.inputmask.positionColorMask(e,k)}}if(l.prototype.positionColorMask=function(e,t){e.style.left=t.offsetLeft+"px"},a!==i)switch(a.action){case"isComplete":return g=a.el,ie(L());case"unmaskedvalue":return g!==i&&a.value===i||($=a.value,$=(e.isFunction(u.onBeforeMask)&&u.onBeforeMask.call(k,$,u)||$).split(""),Z.call(this,i,!1,!1,$),e.isFunction(u.onBeforeWrite)&&u.onBeforeWrite.call(k,i,L(),0,u)),J(g);case"mask":!function(t){z.off(t);var a=function(t,a){var r=t.getAttribute("type"),o="INPUT"===t.tagName&&-1!==e.inArray(r,a.supportsInputType)||t.isContentEditable||"TEXTAREA"===t.tagName;if(!o)if("INPUT"===t.tagName){var s=n.createElement("input");s.setAttribute("type",r),o="text"===s.type,s=null}else o="partial";return!1!==o?function(t){var r,o,s;function l(){return this.inputmask?this.inputmask.opts.autoUnmask?this.inputmask.unmaskedvalue():-1!==w()||!0!==a.nullable?n.activeElement===this&&a.clearMaskOnLostFocus?(b?ne(L().slice()).reverse():ne(L().slice())).join(""):r.call(this):"":r.call(this)}function c(t){o.call(this,t),this.inputmask&&e(this).trigger("setvalue",[t])}if(!t.inputmask.__valueGet){if(!0!==a.noValuePatching){if(Object.getOwnPropertyDescriptor){"function"!=typeof Object.getPrototypeOf&&(Object.getPrototypeOf="object"==typeof"test".__proto__?function(e){return e.__proto__}:function(e){return e.constructor.prototype});var u=Object.getPrototypeOf?Object.getOwnPropertyDescriptor(Object.getPrototypeOf(t),"value"):i;u&&u.get&&u.set?(r=u.get,o=u.set,Object.defineProperty(t,"value",{get:l,set:c,configurable:!0})):"INPUT"!==t.tagName&&(r=function(){return this.textContent},o=function(e){this.textContent=e},Object.defineProperty(t,"value",{get:l,set:c,configurable:!0}))}else n.__lookupGetter__&&t.__lookupGetter__("value")&&(r=t.__lookupGetter__("value"),o=t.__lookupSetter__("value"),t.__defineGetter__("value",l),t.__defineSetter__("value",c));t.inputmask.__valueGet=r,t.inputmask.__valueSet=o}t.inputmask._valueGet=function(e){return b&&!0!==e?r.call(this.el).split("").reverse().join(""):r.call(this.el)},t.inputmask._valueSet=function(e,t){o.call(this.el,null===e||e===i?"":!0!==t&&b?e.split("").reverse().join(""):e)},r===i&&(r=function(){return this.value},o=function(e){this.value=e},function(t){if(e.valHooks&&(e.valHooks[t]===i||!0!==e.valHooks[t].inputmaskpatch)){var n=e.valHooks[t]&&e.valHooks[t].get?e.valHooks[t].get:function(e){return e.value},r=e.valHooks[t]&&e.valHooks[t].set?e.valHooks[t].set:function(e,t){return e.value=t,e};e.valHooks[t]={get:function(e){if(e.inputmask){if(e.inputmask.opts.autoUnmask)return e.inputmask.unmaskedvalue();var t=n(e);return-1!==w(i,i,e.inputmask.maskset.validPositions)||!0!==a.nullable?t:""}return n(e)},set:function(t,n){var i,a=e(t);return i=r(t,n),t.inputmask&&a.trigger("setvalue",[n]),i},inputmaskpatch:!0}}}(t.type),s=t,z.on(s,"mouseenter",function(t){var n=e(this);this.inputmask._valueGet()!==L().join("")&&n.trigger("setvalue")}))}}(t):t.inputmask=i,o}(t,u);if(!1!==a&&(m=e(g=t),-1===(d=g!==i?g.maxLength:i)&&(d=i),!0===u.colorMask&&re(g),r&&("inputmode"in g&&(g.inputmode=u.inputmode,g.setAttribute("inputmode",u.inputmode)),!0===u.disablePredictiveText&&("autocorrect"in g?g.autocorrect=!1:(!0!==u.colorMask&&re(g),g.type="password"))),!0===a&&(g.setAttribute("im-insert",u.insertMode),z.on(g,"submit",X.submitEvent),z.on(g,"reset",X.resetEvent),z.on(g,"blur",X.blurEvent),z.on(g,"focus",X.focusEvent),!0!==u.colorMask&&(z.on(g,"click",X.clickEvent),z.on(g,"mouseleave",X.mouseleaveEvent),z.on(g,"mouseenter",X.mouseenterEvent)),z.on(g,"paste",X.pasteEvent),z.on(g,"cut",X.cutEvent),z.on(g,"complete",u.oncomplete),z.on(g,"incomplete",u.onincomplete),z.on(g,"cleared",u.oncleared),r||!0===u.inputEventOnly?g.removeAttribute("maxLength"):(z.on(g,"keydown",X.keydownEvent),z.on(g,"keypress",X.keypressEvent)),z.on(g,"input",X.inputFallBackEvent),z.on(g,"beforeinput",X.beforeInputEvent)),z.on(g,"setvalue",X.setValueEvent),h=B().join(""),""!==g.inputmask._valueGet(!0)||!1===u.clearMaskOnLostFocus||n.activeElement===g)){var o=e.isFunction(u.onBeforeMask)&&u.onBeforeMask.call(k,g.inputmask._valueGet(!0),u)||g.inputmask._valueGet(!0);""!==o&&Z(g,!0,!1,o.split(""));var s=L().slice();h=s.join(""),!1===ie(s)&&u.clearIncomplete&&_(),u.clearMaskOnLostFocus&&n.activeElement!==g&&(-1===w()?s=[]:ne(s)),(!1===u.clearMaskOnLostFocus||u.showMaskOnFocus&&n.activeElement===g||""!==g.inputmask._valueGet(!0))&&U(g,s),n.activeElement===g&&ee(g,q(w()))}}(g);break;case"format":return $=(e.isFunction(u.onBeforeMask)&&u.onBeforeMask.call(k,a.value,u)||a.value).split(""),Z.call(this,i,!0,!1,$),a.metadata?{value:b?L().slice().reverse().join(""):L().join(""),metadata:p.call(this,{action:"getmetadata"},c,u)}:b?L().slice().reverse().join(""):L().join("");case"isValid":a.value?($=a.value.split(""),Z.call(this,i,!0,!0,$)):a.value=L().join("");for(var se=L(),le=te(),ce=se.length-1;ce>le&&!H(ce);ce--);return se.splice(le,ce+1-le),ie(se)&&a.value===L().join("");case"getemptymask":return B().join("");case"remove":if(g&&g.inputmask)e.data(g,"_inputmask_opts",null),m=e(g),g.inputmask._valueSet(u.autoUnmask?J(g):g.inputmask._valueGet(!0)),z.off(g),g.inputmask.colorMask&&((v=g.inputmask.colorMask).removeChild(g),v.parentNode.insertBefore(g,v),v.parentNode.removeChild(v)),Object.getOwnPropertyDescriptor&&Object.getPrototypeOf?Object.getOwnPropertyDescriptor(Object.getPrototypeOf(g),"value")&&g.inputmask.__valueGet&&Object.defineProperty(g,"value",{get:g.inputmask.__valueGet,set:g.inputmask.__valueSet,configurable:!0}):n.__lookupGetter__&&g.__lookupGetter__("value")&&g.inputmask.__valueGet&&(g.__defineGetter__("value",g.inputmask.__valueGet),g.__defineSetter__("value",g.inputmask.__valueSet)),g.inputmask=i;return g;case"getmetadata":if(e.isArray(c.metadata)){var ue=x(!0,0,!1).join("");return e.each(c.metadata,function(e,t){if(t.mask===ue)return ue=t,!1}),ue}return c.metadata}}return l.prototype={dataAttribute:"data-inputmask",defaults:{placeholder:"_",optionalmarker:["[","]"],quantifiermarker:["{","}"],groupmarker:["(",")"],alternatormarker:"|",escapeChar:"\\",mask:null,regex:null,oncomplete:e.noop,onincomplete:e.noop,oncleared:e.noop,repeat:0,greedy:!1,autoUnmask:!1,removeMaskOnSubmit:!1,clearMaskOnLostFocus:!0,insertMode:!0,clearIncomplete:!1,alias:null,onKeyDown:e.noop,onBeforeMask:null,onBeforePaste:function(t,n){return e.isFunction(n.onBeforeMask)?n.onBeforeMask.call(this,t,n):t},onBeforeWrite:null,onUnMask:null,showMaskOnFocus:!0,showMaskOnHover:!0,onKeyValidation:e.noop,skipOptionalPartCharacter:" ",numericInput:!1,rightAlign:!1,undoOnEscape:!0,radixPoint:"",_radixDance:!1,groupSeparator:"",keepStatic:null,positionCaretOnTab:!0,tabThrough:!1,supportsInputType:["text","tel","password","search"],ignorables:[8,9,13,19,27,33,34,35,36,37,38,39,40,45,46,93,112,113,114,115,116,117,118,119,120,121,122,123,0,229],isComplete:null,preValidation:null,postValidation:null,staticDefinitionSymbol:i,jitMasking:!1,nullable:!0,inputEventOnly:!1,noValuePatching:!1,positionCaretOnClick:"lvp",casing:null,inputmode:"verbatim",colorMask:!1,disablePredictiveText:!1,importDataAttributes:!0},definitions:{9:{validator:"[0-9１-９]",definitionSymbol:"*"},a:{validator:"[A-Za-zА-яЁёÀ-ÿµ]",definitionSymbol:"*"},"*":{validator:"[0-9１-９A-Za-zА-яЁёÀ-ÿµ]"}},aliases:{},masksCache:{},mask:function(a){var r=this;return"string"==typeof a&&(a=n.getElementById(a)||n.querySelectorAll(a)),a=a.nodeName?[a]:a,e.each(a,function(n,a){var o=e.extend(!0,{},r.opts);if(function(n,a,r,o){if(!0===a.importDataAttributes){var s,l,u,f,p=n.getAttribute(o);function h(e,a){null!==(a=a!==i?a:n.getAttribute(o+"-"+e))&&("string"==typeof a&&(0===e.indexOf("on")?a=t[a]:"false"===a?a=!1:"true"===a&&(a=!0)),r[e]=a)}if(p&&""!==p&&(p=p.replace(/'/g,'"'),l=JSON.parse("{"+p+"}")),l)for(f in u=i,l)if("alias"===f.toLowerCase()){u=l[f];break}for(s in h("alias",u),r.alias&&c(r.alias,r,a),a){if(l)for(f in u=i,l)if(f.toLowerCase()===s.toLowerCase()){u=l[f];break}h(s,u)}}return e.extend(!0,a,r),("rtl"===n.dir||a.rightAlign)&&(n.style.textAlign="right"),("rtl"===n.dir||a.numericInput)&&(n.dir="ltr",n.removeAttribute("dir"),a.isRTL=!0),Object.keys(r).length}(a,o,e.extend(!0,{},r.userOptions),r.dataAttribute)){var s=u(o,r.noMasksCache);s!==i&&(a.inputmask!==i&&(a.inputmask.opts.autoUnmask=!0,a.inputmask.remove()),a.inputmask=new l(i,i,!0),a.inputmask.opts=o,a.inputmask.noMasksCache=r.noMasksCache,a.inputmask.userOptions=e.extend(!0,{},r.userOptions),a.inputmask.isRTL=o.isRTL||o.numericInput,a.inputmask.el=a,a.inputmask.maskset=s,e.data(a,"_inputmask_opts",o),p.call(a.inputmask,{action:"mask"}))}}),a&&a[0]&&a[0].inputmask||this},option:function(t,n){return"string"==typeof t?this.opts[t]:"object"==typeof t?(e.extend(this.userOptions,t),this.el&&!0!==n&&this.mask(this.el),this):void 0},unmaskedvalue:function(e){return this.maskset=this.maskset||u(this.opts,this.noMasksCache),p.call(this,{action:"unmaskedvalue",value:e})},remove:function(){return p.call(this,{action:"remove"})},getemptymask:function(){return this.maskset=this.maskset||u(this.opts,this.noMasksCache),p.call(this,{action:"getemptymask"})},hasMaskedValue:function(){return!this.opts.autoUnmask},isComplete:function(){return this.maskset=this.maskset||u(this.opts,this.noMasksCache),p.call(this,{action:"isComplete"})},getmetadata:function(){return this.maskset=this.maskset||u(this.opts,this.noMasksCache),p.call(this,{action:"getmetadata"})},isValid:function(e){return this.maskset=this.maskset||u(this.opts,this.noMasksCache),p.call(this,{action:"isValid",value:e})},format:function(e,t){return this.maskset=this.maskset||u(this.opts,this.noMasksCache),p.call(this,{action:"format",value:e,metadata:t})},setValue:function(t){this.el&&e(this.el).trigger("setvalue",[t])},analyseMask:function(t,n,a){var r,o,s,c,u,f,p=/(?:[?*+]|\{[0-9\+\*]+(?:,[0-9\+\*]*)?(?:\|[0-9\+\*]*)?\})|[^.?*+^${[]()|\\]+|./g,h=/\[\^?]?(?:[^\\\]]+|\\[\S\s]?)*]?|\\(?:0(?:[0-3][0-7]{0,2}|[4-7][0-7]?)?|[1-9][0-9]*|x[0-9A-Fa-f]{2}|u[0-9A-Fa-f]{4}|c[A-Za-z]|[\S\s]?)|\((?:\?[:=!]?)?|(?:[?*+]|\{[0-9]+(?:,[0-9]*)?\})\??|[^.?*+^${[()|\\]+|./g,m=!1,d=new g,v=[],k=[];function g(e,t,n,i){this.matches=[],this.openGroup=e||!1,this.alternatorGroup=!1,this.isGroup=e||!1,this.isOptional=t||!1,this.isQuantifier=n||!1,this.isAlternator=i||!1,this.quantifier={min:1,max:1}}function b(t,r,o){o=o!==i?o:t.matches.length;var s=t.matches[o-1];if(n)0===r.indexOf("[")||m&&/\\d|\\s|\\w]/i.test(r)||"."===r?t.matches.splice(o++,0,{fn:new RegExp(r,a.casing?"i":""),optionality:!1,newBlockMarker:s===i?"master":s.def!==r,casing:null,def:r,placeholder:i,nativeDef:r}):(m&&(r=r[r.length-1]),e.each(r.split(""),function(e,n){s=t.matches[o-1],t.matches.splice(o++,0,{fn:null,optionality:!1,newBlockMarker:s===i?"master":s.def!==n&&null!==s.fn,casing:null,def:a.staticDefinitionSymbol||n,placeholder:a.staticDefinitionSymbol!==i?n:i,nativeDef:(m?"'":"")+n})})),m=!1;else{var c=(a.definitions?a.definitions[r]:i)||l.prototype.definitions[r];c&&!m?t.matches.splice(o++,0,{fn:c.validator?"string"==typeof c.validator?new RegExp(c.validator,a.casing?"i":""):new function(){this.test=c.validator}:new RegExp("."),optionality:!1,newBlockMarker:s===i?"master":s.def!==(c.definitionSymbol||r),casing:c.casing,def:c.definitionSymbol||r,placeholder:c.placeholder,nativeDef:r}):(t.matches.splice(o++,0,{fn:null,optionality:!1,newBlockMarker:s===i?"master":s.def!==r&&null!==s.fn,casing:null,def:a.staticDefinitionSymbol||r,placeholder:a.staticDefinitionSymbol!==i?r:i,nativeDef:(m?"'":"")+r}),m=!1)}}function y(){if(v.length>0){if(b(c=v[v.length-1],o),c.isAlternator){u=v.pop();for(var e=0;e<u.matches.length;e++)u.matches[e].isGroup&&(u.matches[e].isGroup=!1);v.length>0?(c=v[v.length-1]).matches.push(u):d.matches.push(u)}}else b(d,o)}function P(e){var t=new g(!0);return t.openGroup=!1,t.matches=e,t}for(n&&(a.optionalmarker[0]=i,a.optionalmarker[1]=i);r=n?h.exec(t):p.exec(t);){if(o=r[0],n)switch(o.charAt(0)){case"?":o="{0,1}";break;case"+":case"*":o="{"+o+"}"}if(m)y();else switch(o.charAt(0)){case"(?=":case"(?!":case"(?<=":case"(?<!":break;case a.escapeChar:m=!0,n&&y();break;case a.optionalmarker[1]:case a.groupmarker[1]:if((s=v.pop()).openGroup=!1,s!==i)if(v.length>0){if((c=v[v.length-1]).matches.push(s),c.isAlternator){u=v.pop();for(var C=0;C<u.matches.length;C++)u.matches[C].isGroup=!1,u.matches[C].alternatorGroup=!1;v.length>0?(c=v[v.length-1]).matches.push(u):d.matches.push(u)}}else d.matches.push(s);else y();break;case a.optionalmarker[0]:v.push(new g(!1,!0));break;case a.groupmarker[0]:v.push(new g(!0));break;case a.quantifiermarker[0]:var E=new g(!1,!1,!0),x=(o=o.replace(/[{}]/g,"")).split("|"),A=x[0].split(","),_=isNaN(A[0])?A[0]:parseInt(A[0]),w=1===A.length?_:isNaN(A[1])?A[1]:parseInt(A[1]);"*"!==_&&"+"!==_||(_="*"===w?0:1),E.quantifier={min:_,max:w,jit:x[1]};var O=v.length>0?v[v.length-1].matches:d.matches;if((r=O.pop()).isAlternator){O.push(r),O=r.matches;var S=new g(!0),j=O.pop();O.push(S),O=S.matches,r=j}r.isGroup||(r=P([r])),O.push(r),O.push(E);break;case a.alternatormarker:function M(e){var t=e.pop();return t.isQuantifier&&(t=P([e.pop(),t])),t}if(v.length>0){var D=(c=v[v.length-1]).matches[c.matches.length-1];f=c.openGroup&&(D.matches===i||!1===D.isGroup&&!1===D.isAlternator)?v.pop():M(c.matches)}else f=M(d.matches);if(f.isAlternator)v.push(f);else if(f.alternatorGroup?(u=v.pop(),f.alternatorGroup=!1):u=new g(!1,!1,!1,!0),u.matches.push(f),v.push(u),f.openGroup){f.openGroup=!1;var G=new g(!0);G.alternatorGroup=!0,v.push(G)}break;default:y()}}for(;v.length>0;)s=v.pop(),d.matches.push(s);return d.matches.length>0&&(!function t(r){r&&r.matches&&e.each(r.matches,function(e,o){var s=r.matches[e+1];(s===i||s.matches===i||!1===s.isQuantifier)&&o&&o.isGroup&&(o.isGroup=!1,n||(b(o,a.groupmarker[0],0),!0!==o.openGroup&&b(o,a.groupmarker[1]))),t(o)})}(d),k.push(d)),(a.numericInput||a.isRTL)&&function e(t){for(var n in t.matches=t.matches.reverse(),t.matches)if(t.matches.hasOwnProperty(n)){var r=parseInt(n);if(t.matches[n].isQuantifier&&t.matches[r+1]&&t.matches[r+1].isGroup){var o=t.matches[n];t.matches.splice(n,1),t.matches.splice(r+1,0,o)}t.matches[n].matches!==i?t.matches[n]=e(t.matches[n]):t.matches[n]=((s=t.matches[n])===a.optionalmarker[0]?s=a.optionalmarker[1]:s===a.optionalmarker[1]?s=a.optionalmarker[0]:s===a.groupmarker[0]?s=a.groupmarker[1]:s===a.groupmarker[1]&&(s=a.groupmarker[0]),s)}var s;return t}(k[0]),k}},l.extendDefaults=function(t){e.extend(!0,l.prototype.defaults,t)},l.extendDefinitions=function(t){e.extend(!0,l.prototype.definitions,t)},l.extendAliases=function(t){e.extend(!0,l.prototype.aliases,t)},l.format=function(e,t,n){return l(t).format(e,n)},l.unmask=function(e,t){return l(t).unmaskedvalue(e)},l.isValid=function(e,t){return l(t).isValid(e)},l.remove=function(t){"string"==typeof t&&(t=n.getElementById(t)||n.querySelectorAll(t)),t=t.nodeName?[t]:t,e.each(t,function(e,t){t.inputmask&&t.inputmask.remove()})},l.setValue=function(t,i){"string"==typeof t&&(t=n.getElementById(t)||n.querySelectorAll(t)),t=t.nodeName?[t]:t,e.each(t,function(t,n){n.inputmask?n.inputmask.setValue(i):e(n).trigger("setvalue",[i])})},l.escapeRegex=function(e){return e.replace(new RegExp("(\\"+["/",".","*","+","?","|","(",")","[","]","{","}","\\","$","^"].join("|\\")+")","gim"),"\\$1")},l.keyCode={BACKSPACE:8,BACKSPACE_SAFARI:127,DELETE:46,DOWN:40,END:35,ENTER:13,ESCAPE:27,HOME:36,INSERT:45,LEFT:37,PAGE_DOWN:34,PAGE_UP:33,RIGHT:39,SPACE:32,TAB:9,UP:38,X:88,CONTROL:17},l});