@echo off
setlocal enabledelayedexpansion
color 0F
title Laravel Botble CMS - Master Setup Controller

echo.
echo ███████╗███████╗████████╗██╗   ██╗██████╗ 
echo ██╔════╝██╔════╝╚══██╔══╝██║   ██║██╔══██╗
echo ███████╗█████╗     ██║   ██║   ██║██████╔╝
echo ╚════██║██╔══╝     ██║   ██║   ██║██╔═══╝ 
echo ███████║███████╗   ██║   ╚██████╔╝██║     
echo ╚══════╝╚══════╝   ╚═╝    ╚═════╝ ╚═╝     
echo.
echo     LARAVEL BOTBLE CMS SETUP MASTER
echo ===============================================
echo.

set "APP_PATH=c:\xampp\htdocs\main"
set "XAMPP_PATH=c:\xampp"

echo Welcome to the Laravel Botble CMS Setup Master!
echo.
echo This tool will help you get your application running with XAMPP.
echo.
echo Current Status Check:
echo ===============================================

:: Quick status check
if not exist "%XAMPP_PATH%" (
    echo ❌ XAMPP not found at %XAMPP_PATH%
    echo    Please install XAMPP first, then run this script again.
    echo.
    pause
    exit /b 1
) else (
    echo ✅ XAMPP found at %XAMPP_PATH%
)

if not exist "%APP_PATH%\artisan" (
    echo ❌ Laravel application not found at %APP_PATH%
    echo    Please ensure your application is in the correct location.
    echo.
    pause
    exit /b 1
) else (
    echo ✅ Laravel application found
)

:: Check if Apache is running
netstat -an | findstr ":80 " >nul
if errorlevel 1 (
    echo ⚠️  Apache not running - will need to start XAMPP services
) else (
    echo ✅ Apache appears to be running
)

echo.
echo What would you like to do?
echo ===============================================
echo.
echo 1. 🚀 Complete Automated Setup (Recommended)
echo    - Verify XAMPP installation
echo    - Configure Laravel application  
echo    - Set up database and run migrations
echo    - Test application accessibility
echo    - Install/verify Botble CMS
echo.
echo 2. 🔧 Install/Reinstall Botble CMS Only
echo    - For when Laravel is working but CMS needs setup
echo.
echo 3. 🩺 Troubleshoot Issues
echo    - Fix common problems
echo    - Clear caches
echo    - Repair permissions
echo    - View error logs
echo.
echo 4. 📖 View Quick Start Guide
echo    - Manual setup instructions
echo    - Common issues and solutions
echo.
echo 5. 🧪 Test Application Status
echo    - Check if everything is working
echo    - Test URLs and connectivity
echo.
echo 6. ❌ Exit
echo.

set /p choice="Enter your choice (1-6): "

if "%choice%"=="1" goto :complete_setup
if "%choice%"=="2" goto :cms_install
if "%choice%"=="3" goto :troubleshoot
if "%choice%"=="4" goto :quick_guide
if "%choice%"=="5" goto :test_status
if "%choice%"=="6" goto :exit
goto :invalid_choice

:complete_setup
echo.
echo Starting Complete Automated Setup...
echo ===============================================
echo.
echo This will:
echo - Verify your XAMPP installation
echo - Configure your Laravel application
echo - Set up the database
echo - Install Botble CMS
echo - Test everything is working
echo.
set /p confirm="Continue? (Y/N): "
if /i not "%confirm%"=="Y" goto :main_menu

call check_setup.bat
goto :end

:cms_install
echo.
echo Starting CMS Installation...
echo ===============================================
call install_cms.bat
goto :end

:troubleshoot
echo.
echo Starting Troubleshooting Tool...
echo ===============================================
call troubleshoot.bat
goto :end

:quick_guide
echo.
echo Opening Quick Start Guide...
echo ===============================================
if exist "QUICK_START_GUIDE.md" (
    start "" "QUICK_START_GUIDE.md"
) else (
    echo Quick Start Guide not found.
)
echo.
echo Press any key to return to main menu...
pause >nul
goto :main_menu

:test_status
echo.
echo Testing Application Status...
echo ===============================================

cd /d "%APP_PATH%"

echo Testing PHP version...
"%XAMPP_PATH%\php\php.exe" --version | findstr "PHP"

echo.
echo Testing Laravel...
"%XAMPP_PATH%\php\php.exe" artisan --version

echo.
echo Testing application accessibility...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost/main' -UseBasicParsing -TimeoutSec 5; Write-Host 'Main App Status:' $response.StatusCode } catch { Write-Host 'Main App Error:' $_.Exception.Message }"

echo.
echo Testing admin panel...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost/main/admin' -UseBasicParsing -TimeoutSec 5; Write-Host 'Admin Panel Status:' $response.StatusCode } catch { Write-Host 'Admin Panel Error:' $_.Exception.Message }"

echo.
echo Status check complete.
echo.
echo Press any key to return to main menu...
pause >nul
goto :main_menu

:invalid_choice
echo.
echo Invalid choice. Please try again.
echo.
pause
goto :main_menu

:main_menu
cls
goto :start

:exit
echo.
echo Thank you for using Laravel Botble CMS Setup Master!
echo.
echo 🌐 Your application URLs:
echo    Main Site: http://localhost/main
echo    Admin Panel: http://localhost/main/admin
echo.
echo 🔑 Default admin credentials:
echo    Email: <EMAIL>
echo    Password: 159357
echo.
echo Have a great day! 🎉
echo.

:end
pause
