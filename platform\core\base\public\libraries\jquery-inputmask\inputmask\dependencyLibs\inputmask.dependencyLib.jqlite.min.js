/*!
* dependencyLibs/inputmask.dependencyLib.jqlite.min.js
* https://github.com/RobinHerbots/Inputmask
* Copyright (c) 2010 - 2018 <PERSON>
* Licensed under the MIT license (http://www.opensource.org/licenses/mit-license.php)
* Version: 4.0.1-beta.3
*/

!function(e){"function"==typeof define&&define.amd?define(["jqlite","../global/window","../global/document"],e):"object"==typeof exports?module.exports=e(require("jqlite"),require("../global/window"),require("../global/document")):window.dependencyLib=e(jqlite,window,document)}(function(e,n,t){function o(e){return null!=e&&e===e.window}return e.inArray=function(e,n,t){return null==n?-1:function(e,n){for(var t=0,o=e.length;t<o;t++)if(e[t]===n)return t;return-1}(n,e)},e.isFunction=function(e){return"function"==typeof e},e.isArray=Array.isArray,e.isPlainObject=function(e){return"object"==typeof e&&!e.nodeType&&!o(e)&&!(e.constructor&&!Object.hasOwnProperty.call(e.constructor.prototype,"isPrototypeOf"))},e.extend=function(){var n,t,o,r,i,u,l=arguments[0]||{},c=1,a=arguments.length,f=!1;for("boolean"==typeof l&&(f=l,l=arguments[c]||{},c++),"object"==typeof l||e.isFunction(l)||(l={}),c===a&&(l=this,c--);c<a;c++)if(null!=(n=arguments[c]))for(t in n)o=l[t],l!==(r=n[t])&&(f&&r&&(e.isPlainObject(r)||(i=e.isArray(r)))?(i?(i=!1,u=o&&e.isArray(o)?o:[]):u=o&&e.isPlainObject(o)?o:{},l[t]=e.extend(f,u,r)):void 0!==r&&(l[t]=r));return l},e.each=function(e,n){var t,r,i,u=0;if(r="length"in(t=e)&&t.length,"function"!==(i=typeof t)&&!o(t)&&(1===t.nodeType&&r||"array"===i||0===r||"number"==typeof r&&r>0&&r-1 in t))for(var l=e.length;u<l&&!1!==n.call(e[u],u,e[u]);u++);else for(u in e)if(!1===n.call(e[u],u,e[u]))break;return e},e.data=function(n,t,o){return e(n).data(t,o)},e.Event=e.Event||function(e,n){n=n||{bubbles:!1,cancelable:!1,detail:void 0};var o=t.createEvent("CustomEvent");return o.initCustomEvent(e,n.bubbles,n.cancelable,n.detail),o},e.Event.prototype=n.Event.prototype,e});