.dd-list .dd-item>button{font-size:1rem;inset-inline-end:.55rem;position:absolute;top:.5rem}.dd-list .dd-placeholder{background:rgba(var(--bb-info-rgb),.1);border:var(--bb-card-border-width) dashed var(--bb-border-color);box-sizing:border-box;display:block;font-size:13px;line-height:20px;margin:0;min-height:30px;padding:0;position:relative}.dd-list .dd3-handle{background:var(--bb-body-bg);border:var(--bb-list-group-border-width) solid var(--bb-list-group-border-color);border-bottom-right-radius:0;border-top-right-radius:0;cursor:move;height:40px;left:0;margin:0;overflow:hidden;position:absolute;text-indent:100%;top:5px;white-space:nowrap;width:30px}.dd-list .dd3-handle:before{color:var(--bb-body-color);content:"≡";display:block;font-size:20px;font-weight:400;left:0;position:absolute;text-align:center;text-indent:0;top:8px;width:100%}.dd-list .dd3-content{background:var(--bb-bg-surface);border:var(--bb-list-group-border-width) solid var(--bb-list-group-border-color);border-radius:3px;box-sizing:border-box;color:inherit;display:block;font-weight:400;height:40px;margin:5px 0;padding:5px 10px 5px 40px;text-decoration:none}.dd-list .dd3-content .badge{margin-inline-start:.25rem}.dd-list .dd3-content.active .fetch-data{font-weight:700}.dd-list .dd3-content.active .delete-button{display:flex}.dd-list .dd3-content .delete-button{display:none}.dd .dd-empty{align-items:center;color:var(--bb-body-color);display:flex;justify-content:center}
