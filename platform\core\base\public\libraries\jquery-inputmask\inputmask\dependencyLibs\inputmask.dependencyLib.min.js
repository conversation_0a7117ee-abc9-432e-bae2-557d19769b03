/*!
* dependencyLibs/inputmask.dependencyLib.min.js
* https://github.com/RobinHerbots/Inputmask
* Copyright (c) 2010 - 2018 <PERSON>
* Licensed under the MIT license (http://www.opensource.org/licenses/mit-license.php)
* Version: 4.0.1-beta.3
*/

!function(e){"function"==typeof define&&define.amd?define(["../global/window","../global/document"],e):"object"==typeof exports?module.exports=e(require("../global/window"),require("../global/document")):window.dependencyLib=e(window,document)}(function(e,t){function n(e){return null!=e&&e===e.window}function i(e){return e instanceof Element}function o(n){return n instanceof o?n:this instanceof o?void(void 0!==n&&null!==n&&n!==e&&(this[0]=n.nodeName?n:void 0!==n[0]&&n[0].nodeName?n[0]:t.querySelector(n),void 0!==this[0]&&null!==this[0]&&(this[0].eventRegistry=this[0].eventRegistry||{}))):new o(n)}return o.prototype={on:function(e,t){if(i(this[0])){var n=this[0].eventRegistry,o=this[0];for(var r=e.split(" "),a=0;a<r.length;a++){var l=r[a].split("."),s=l[0],f=l[1]||"global";c=s,u=f,o.addEventListener?o.addEventListener(c,t,!1):o.attachEvent&&o.attachEvent("on"+c,t),n[c]=n[c]||{},n[c][u]=n[c][u]||[],n[c][u].push(t)}}var c,u;return this},off:function(e,t){if(i(this[0])){var n=this[0].eventRegistry,o=this[0];function r(e,t,i){if(e in n==!0)if(o.removeEventListener?o.removeEventListener(e,i,!1):o.detachEvent&&o.detachEvent("on"+e,i),"global"===t)for(var r in n[e])n[e][r].splice(n[e][r].indexOf(i),1);else n[e][t].splice(n[e][t].indexOf(i),1)}function a(e,i){var o,r,a=[];if(e.length>0)if(void 0===t)for(o=0,r=n[e][i].length;o<r;o++)a.push({ev:e,namespace:i&&i.length>0?i:"global",handler:n[e][i][o]});else a.push({ev:e,namespace:i&&i.length>0?i:"global",handler:t});else if(i.length>0)for(var l in n)for(var s in n[l])if(s===i)if(void 0===t)for(o=0,r=n[l][s].length;o<r;o++)a.push({ev:l,namespace:s,handler:n[l][s][o]});else a.push({ev:l,namespace:s,handler:t});return a}for(var l=e.split(" "),s=0;s<l.length;s++)for(var f=l[s].split("."),c=a(f[0],f[1]),u=0,v=c.length;u<v;u++)r(c[u].ev,c[u].namespace,c[u].handler)}return this},trigger:function(e){if(i(this[0]))for(var n=this[0].eventRegistry,r=this[0],a="string"==typeof e?e.split(" "):[e.type],l=0;l<a.length;l++){var s=a[l].split("."),f=s[0],c=s[1]||"global";if(void 0!==t&&"global"===c){var u,v,d={bubbles:!0,cancelable:!0,detail:arguments[1]};if(t.createEvent){try{u=new CustomEvent(f,d)}catch(e){(u=t.createEvent("CustomEvent")).initCustomEvent(f,d.bubbles,d.cancelable,d.detail)}e.type&&o.extend(u,e),r.dispatchEvent(u)}else(u=t.createEventObject()).eventType=f,u.detail=arguments[1],e.type&&o.extend(u,e),r.fireEvent("on"+u.eventType,u)}else if(void 0!==n[f])if(arguments[0]=arguments[0].type?arguments[0]:o.Event(arguments[0]),"global"===c)for(var p in n[f])for(v=0;v<n[f][p].length;v++)n[f][p][v].apply(r,arguments);else for(v=0;v<n[f][c].length;v++)n[f][c][v].apply(r,arguments)}return this}},o.isFunction=function(e){return"function"==typeof e},o.noop=function(){},o.isArray=Array.isArray,o.inArray=function(e,t,n){return null==t?-1:function(e,t){for(var n=0,i=e.length;n<i;n++)if(e[n]===t)return n;return-1}(t,e)},o.valHooks=void 0,o.isPlainObject=function(e){return"object"==typeof e&&!e.nodeType&&!n(e)&&!(e.constructor&&!Object.hasOwnProperty.call(e.constructor.prototype,"isPrototypeOf"))},o.extend=function(){var e,t,n,i,r,a,l=arguments[0]||{},s=1,f=arguments.length,c=!1;for("boolean"==typeof l&&(c=l,l=arguments[s]||{},s++),"object"==typeof l||o.isFunction(l)||(l={}),s===f&&(l=this,s--);s<f;s++)if(null!=(e=arguments[s]))for(t in e)n=l[t],l!==(i=e[t])&&(c&&i&&(o.isPlainObject(i)||(r=o.isArray(i)))?(r?(r=!1,a=n&&o.isArray(n)?n:[]):a=n&&o.isPlainObject(n)?n:{},l[t]=o.extend(c,a,i)):void 0!==i&&(l[t]=i));return l},o.each=function(e,t){var i,o,r,a=0;if(o="length"in(i=e)&&i.length,"function"!==(r=typeof i)&&!n(i)&&(1===i.nodeType&&o||"array"===r||0===o||"number"==typeof o&&o>0&&o-1 in i))for(var l=e.length;a<l&&!1!==t.call(e[a],a,e[a]);a++);else for(a in e)if(!1===t.call(e[a],a,e[a]))break;return e},o.data=function(e,t,n){if(void 0===n)return e.__data?e.__data[t]:null;e.__data=e.__data||{},e.__data[t]=n},"function"==typeof e.CustomEvent?o.Event=e.CustomEvent:(o.Event=function(e,n){n=n||{bubbles:!1,cancelable:!1,detail:void 0};var i=t.createEvent("CustomEvent");return i.initCustomEvent(e,n.bubbles,n.cancelable,n.detail),i},o.Event.prototype=e.Event.prototype),o});