@echo off
setlocal enabledelayedexpansion
color 0D
title ZERO TOUCH SETUP - Complete Automation

echo.
echo ███████╗███████╗██████╗  ██████╗     ████████╗ ██████╗ ██╗   ██╗ ██████╗██╗  ██╗
echo ╚══███╔╝██╔════╝██╔══██╗██╔═══██╗    ╚══██╔══╝██╔═══██╗██║   ██║██╔════╝██║  ██║
echo   ███╔╝ █████╗  ██████╔╝██║   ██║       ██║   ██║   ██║██║   ██║██║     ███████║
echo  ███╔╝  ██╔══╝  ██╔══██╗██║   ██║       ██║   ██║   ██║██║   ██║██║     ██╔══██║
echo ███████╗███████╗██║  ██║╚██████╔╝       ██║   ╚██████╔╝╚██████╔╝╚██████╗██║  ██║
echo ╚══════╝╚══════╝╚═╝  ╚═╝ ╚═════╝        ╚═╝    ╚═════╝  ╚═════╝  ╚═════╝╚═╝  ╚═╝
echo.
echo                    ZERO TOUCH LARAVEL SETUP
echo                  No Manual Intervention Required
echo ===============================================
echo.

:: Request admin privileges if not already running as admin
net session >nul 2>&1
if errorlevel 1 (
    echo Requesting administrator privileges...
    powershell -Command "Start-Process '%~f0' -Verb RunAs"
    exit /b
)

echo ✓ Running with administrator privileges

set "APP_PATH=c:\xampp\htdocs\main"
set "XAMPP_PATH=c:\xampp"
set "BACKUP_PATH=c:\xampp_backup"
set "TEMP_SETUP=%TEMP%\zero_touch_setup"

mkdir "%TEMP_SETUP%" 2>nul

echo.
echo [ZERO-TOUCH 1] Environment Analysis...
echo ===============================================

cd /d "%APP_PATH%" 2>nul
if errorlevel 1 (
    echo ❌ Laravel app not found at %APP_PATH%
    echo Creating directory structure...
    mkdir "%APP_PATH%" 2>nul
    echo Please ensure your Laravel application is at this location.
    pause
    exit /b 1
)

echo ✓ Laravel application located

:: Kill all XAMPP processes
echo Terminating all XAMPP processes...
taskkill /F /IM httpd.exe 2>nul
taskkill /F /IM mysqld.exe 2>nul
taskkill /F /IM xampp-control.exe 2>nul
taskkill /F /IM apache.exe 2>nul

echo.
echo [ZERO-TOUCH 2] PHP Version Resolution...
echo ===============================================

:: Strategy 1: Check if compatible PHP already exists
if exist "%XAMPP_PATH%\php\php.exe" (
    for /f "tokens=2 delims= " %%a in ('"%XAMPP_PATH%\php\php.exe" --version ^| findstr "PHP"') do (
        set CURRENT_PHP=%%a
    )
    
    echo Current PHP: !CURRENT_PHP!
    echo !CURRENT_PHP! | findstr /r "^8\.[2-9]\|^8\.[1-9][0-9]\|^9\." >nul
    if not errorlevel 1 (
        echo ✓ Compatible PHP found, proceeding with setup
        goto :configure_application
    )
)

:: Strategy 2: Try to use system PHP if compatible
php --version >nul 2>&1
if not errorlevel 1 (
    for /f "tokens=2 delims= " %%a in ('php --version ^| findstr "PHP"') do (
        set SYSTEM_PHP=%%a
    )
    
    echo System PHP: !SYSTEM_PHP!
    echo !SYSTEM_PHP! | findstr /r "^8\.[2-9]\|^8\.[1-9][0-9]\|^9\." >nul
    if not errorlevel 1 (
        echo ✓ Using compatible system PHP
        set "PHP_CMD=php"
        goto :configure_application
    )
)

:: Strategy 3: Download and install portable PHP
echo Downloading portable PHP 8.2...
set "PHP_URL=https://windows.php.net/downloads/releases/php-8.2.12-Win32-vs16-x64.zip"
set "PHP_ZIP=%TEMP_SETUP%\php.zip"
set "PORTABLE_PHP=%TEMP_SETUP%\php"

powershell -Command "try { Write-Host 'Downloading PHP...'; Invoke-WebRequest -Uri '%PHP_URL%' -OutFile '%PHP_ZIP%' -TimeoutSec 300; Write-Host 'Download completed' } catch { Write-Host 'Download failed'; exit 1 }"

if exist "%PHP_ZIP%" (
    echo Extracting portable PHP...
    powershell -Command "Expand-Archive -Path '%PHP_ZIP%' -DestinationPath '%PORTABLE_PHP%' -Force"
    
    if exist "%PORTABLE_PHP%\php.exe" (
        echo ✓ Portable PHP 8.2 ready
        set "PHP_CMD=%PORTABLE_PHP%\php.exe"
        
        :: Configure PHP
        copy "%PORTABLE_PHP%\php.ini-production" "%PORTABLE_PHP%\php.ini" >nul 2>&1
        
        goto :configure_application
    )
)

:: Strategy 4: Download and install XAMPP automatically
echo Attempting XAMPP auto-installation...
set "XAMPP_URL=https://sourceforge.net/projects/xampp/files/XAMPP Windows/8.2.12/xampp-windows-x64-8.2.12-0-VS16-installer.exe"
set "XAMPP_INSTALLER=%TEMP_SETUP%\xampp.exe"

powershell -Command "try { Invoke-WebRequest -Uri '%XAMPP_URL%' -OutFile '%XAMPP_INSTALLER%' -TimeoutSec 600; Write-Host 'XAMPP downloaded' } catch { Write-Host 'XAMPP download failed' }"

if exist "%XAMPP_INSTALLER%" (
    echo Installing XAMPP silently...
    
    :: Backup existing XAMPP if it exists
    if exist "%XAMPP_PATH%" (
        echo Backing up existing XAMPP...
        move "%XAMPP_PATH%" "%BACKUP_PATH%" >nul 2>&1
    )
    
    :: Silent installation
    "%XAMPP_INSTALLER%" /S /D=%XAMPP_PATH%
    
    :: Wait for installation
    timeout /t 60 /nobreak >nul
    
    if exist "%XAMPP_PATH%\php\php.exe" (
        echo ✓ XAMPP installed successfully
        set "PHP_CMD=%XAMPP_PATH%\php\php.exe"
        goto :start_xampp_services
    )
)

echo ❌ All automatic PHP installation methods failed
echo.
echo FALLBACK: Using Laravel's built-in server
echo This will work but requires keeping this window open
echo.
goto :use_builtin_server

:start_xampp_services
echo.
echo [ZERO-TOUCH 3] Starting XAMPP Services...
echo ===============================================

:: Start Apache
if exist "%XAMPP_PATH%\apache\bin\httpd.exe" (
    start "" "%XAMPP_PATH%\apache\bin\httpd.exe" -D FOREGROUND
    echo Apache starting...
)

:: Start MySQL
if exist "%XAMPP_PATH%\mysql\bin\mysqld.exe" (
    start "" "%XAMPP_PATH%\mysql\bin\mysqld.exe" --defaults-file="%XAMPP_PATH%\mysql\bin\my.ini"
    echo MySQL starting...
)

timeout /t 15 /nobreak >nul

:configure_application
echo.
echo [ZERO-TOUCH 4] Application Configuration...
echo ===============================================

if not defined PHP_CMD (
    set "PHP_CMD=%XAMPP_PATH%\php\php.exe"
)

echo Using PHP: %PHP_CMD%
"%PHP_CMD%" --version

:: Install dependencies
echo Installing Composer dependencies...
if exist "%XAMPP_PATH%\composer\composer.bat" (
    "%XAMPP_PATH%\composer\composer.bat" install --no-interaction --optimize-autoloader 2>nul
) else (
    composer install --no-interaction --optimize-autoloader 2>nul
)

:: Database setup
echo Setting up database...
if not exist "database\database.sqlite" (
    type nul > "database\database.sqlite"
)

:: Laravel configuration
echo Configuring Laravel...
"%PHP_CMD%" artisan key:generate --force 2>nul
"%PHP_CMD%" artisan config:clear 2>nul
"%PHP_CMD%" artisan migrate --force 2>nul

:: CMS installation
echo Installing CMS...
"%PHP_CMD%" artisan cms:install --no-interaction 2>nul || (
    "%PHP_CMD%" artisan make:user <EMAIL> "Admin User" 159357 --role=admin 2>nul
    "%PHP_CMD%" artisan db:seed 2>nul
)

:: Optimization
echo Optimizing application...
"%PHP_CMD%" artisan config:cache 2>nul
"%PHP_CMD%" artisan route:cache 2>nul
"%PHP_CMD%" artisan cms:publish:assets 2>nul

echo.
echo [ZERO-TOUCH 5] Testing Application...
echo ===============================================

:: Test if XAMPP setup worked
timeout /t 5 /nobreak >nul
powershell -Command "try { $r = Invoke-WebRequest -Uri 'http://localhost/main' -UseBasicParsing -TimeoutSec 5; Write-Host 'XAMPP Status:' $r.StatusCode } catch { Write-Host 'XAMPP not accessible, using built-in server' }"

:: If XAMPP test failed, use built-in server
powershell -Command "try { Invoke-WebRequest -Uri 'http://localhost/main' -UseBasicParsing -TimeoutSec 5 } catch { exit 1 }"
if errorlevel 1 (
    goto :use_builtin_server
)

echo ✓ Application accessible via XAMPP
goto :success

:use_builtin_server
echo.
echo [ZERO-TOUCH 6] Using Laravel Built-in Server...
echo ===============================================

echo Starting Laravel development server...
start "Laravel Server" "%PHP_CMD%" artisan serve --host=127.0.0.1 --port=8000

timeout /t 10 /nobreak >nul

echo Testing built-in server...
powershell -Command "try { $r = Invoke-WebRequest -Uri 'http://127.0.0.1:8000' -UseBasicParsing -TimeoutSec 10; Write-Host 'Server Status:' $r.StatusCode } catch { Write-Host 'Server Error:' $_.Exception.Message }"

echo.
echo ✓ Application running on built-in server
echo.
echo 🌐 Your application URLs:
echo Main Site:    http://127.0.0.1:8000
echo Admin Panel:  http://127.0.0.1:8000/admin
echo.
echo 🔑 Admin Login:
echo Email:        <EMAIL>
echo Password:     159357
echo.

start "" "http://127.0.0.1:8000"
goto :end

:success
echo.
echo ===============================================
echo     🎉 ZERO TOUCH SETUP COMPLETED! 🎉
echo ===============================================
echo.
echo ✅ PHP 8.2+ environment ready
echo ✅ XAMPP services running
echo ✅ Laravel application configured
echo ✅ Botble CMS installed
echo ✅ Database set up
echo ✅ Application optimized
echo.
echo 🌐 Your application URLs:
echo Main Site:    http://localhost/main
echo Admin Panel:  http://localhost/main/admin
echo.
echo 🔑 Admin Login:
echo Email:        <EMAIL>
echo Password:     159357
echo.

start "" "http://localhost/main"

:end
echo.
echo Setup complete! Your application is now running.
echo.
pause
