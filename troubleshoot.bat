@echo off
setlocal enabledelayedexpansion
color 0C
title Lara<PERSON>ble CMS - Troubleshooting Tool

echo ===============================================
echo     LARAVEL BOTBLE CMS TROUBLESHOOTING
echo ===============================================
echo.

set "APP_PATH=c:\xampp\htdocs\main"
set "XAMPP_PATH=c:\xampp"
set "PHP_CMD=%XAMPP_PATH%\php\php.exe"
set "APP_URL=http://localhost/main"

cd /d "%APP_PATH%"

echo Select troubleshooting option:
echo.
echo 1. Fix Permission Issues
echo 2. Clear All Caches
echo 3. Fix Database Issues
echo 4. Reset Application Key
echo 5. Check System Requirements
echo 6. Fix Apache/XAMPP Issues
echo 7. View Error Logs
echo 8. Complete System Repair
echo 9. Test Application Connectivity
echo.
set /p choice="Enter your choice (1-9): "

if "%choice%"=="1" goto :fix_permissions
if "%choice%"=="2" goto :clear_caches
if "%choice%"=="3" goto :fix_database
if "%choice%"=="4" goto :reset_key
if "%choice%"=="5" goto :check_requirements
if "%choice%"=="6" goto :fix_apache
if "%choice%"=="7" goto :view_logs
if "%choice%"=="8" goto :complete_repair
if "%choice%"=="9" goto :test_connectivity
goto :invalid_choice

:fix_permissions
echo.
echo Fixing Permission Issues...
echo ===============================================

echo Setting storage permissions...
if exist "storage" (
    attrib -R "storage\*" /S /D
    echo ✓ Storage permissions updated
) else (
    mkdir storage
    echo ✓ Storage directory created
)

echo Setting bootstrap cache permissions...
if exist "bootstrap\cache" (
    attrib -R "bootstrap\cache\*" /S /D
    echo ✓ Bootstrap cache permissions updated
) else (
    mkdir "bootstrap\cache"
    echo ✓ Bootstrap cache directory created
)

echo ✓ Permission fixes completed
goto :end

:clear_caches
echo.
echo Clearing All Caches...
echo ===============================================

%PHP_CMD% artisan config:clear
echo ✓ Config cache cleared

%PHP_CMD% artisan cache:clear
echo ✓ Application cache cleared

%PHP_CMD% artisan route:clear
echo ✓ Route cache cleared

%PHP_CMD% artisan view:clear
echo ✓ View cache cleared

%PHP_CMD% artisan optimize:clear
echo ✓ All optimization caches cleared

echo ✓ All caches cleared successfully
goto :end

:fix_database
echo.
echo Fixing Database Issues...
echo ===============================================

echo Checking database connection...
%PHP_CMD% artisan migrate:status
if errorlevel 1 (
    echo Database connection failed. Checking SQLite file...
    if not exist "database\database.sqlite" (
        echo Creating SQLite database file...
        type nul > "database\database.sqlite"
        echo ✓ SQLite database file created
    )
    
    echo Running migrations...
    %PHP_CMD% artisan migrate --force
    echo ✓ Database migrations completed
) else (
    echo ✓ Database connection is working
)

goto :end

:reset_key
echo.
echo Resetting Application Key...
echo ===============================================

%PHP_CMD% artisan key:generate --force
echo ✓ New application key generated

%PHP_CMD% artisan config:cache
echo ✓ Configuration cached

echo ✓ Application key reset completed
goto :end

:check_requirements
echo.
echo Checking System Requirements...
echo ===============================================

echo PHP Version:
%PHP_CMD% --version

echo.
echo PHP Extensions:
%PHP_CMD% -m | findstr -i "curl gd json pdo zip"

echo.
echo Laravel Version:
%PHP_CMD% artisan --version

echo.
echo Composer Version:
composer --version 2>nul || echo Composer not found in PATH

echo.
echo Disk Space:
dir "%APP_PATH%" | findstr "bytes free"

goto :end

:fix_apache
echo.
echo Fixing Apache/XAMPP Issues...
echo ===============================================

echo Checking if Apache is running...
netstat -an | findstr ":80 " >nul
if errorlevel 1 (
    echo Apache is not running on port 80
    echo.
    echo Attempting to start Apache...
    if exist "%XAMPP_PATH%\apache_start.bat" (
        call "%XAMPP_PATH%\apache_start.bat"
    ) else (
        echo Please start Apache manually from XAMPP Control Panel
    )
) else (
    echo ✓ Apache is running on port 80
)

echo.
echo Testing application accessibility...
powershell -Command "try { $response = Invoke-WebRequest -Uri '%APP_URL%' -UseBasicParsing -TimeoutSec 5; Write-Host 'Status:' $response.StatusCode } catch { Write-Host 'Error:' $_.Exception.Message }"

goto :end

:view_logs
echo.
echo Viewing Error Logs...
echo ===============================================

echo Laravel Log (last 20 lines):
if exist "storage\logs\laravel.log" (
    powershell -Command "Get-Content 'storage\logs\laravel.log' -Tail 20"
) else (
    echo No Laravel log file found
)

echo.
echo Apache Error Log (if accessible):
if exist "%XAMPP_PATH%\apache\logs\error.log" (
    powershell -Command "Get-Content '%XAMPP_PATH%\apache\logs\error.log' -Tail 10"
) else (
    echo Apache error log not accessible
)

goto :end

:complete_repair
echo.
echo Complete System Repair...
echo ===============================================

echo This will perform a comprehensive repair of your application.
set /p confirm="Continue? (Y/N): "
if /i not "%confirm%"=="Y" goto :end

echo.
echo Step 1: Clearing all caches...
%PHP_CMD% artisan optimize:clear

echo Step 2: Fixing permissions...
if exist "storage" attrib -R "storage\*" /S /D
if exist "bootstrap\cache" attrib -R "bootstrap\cache\*" /S /D

echo Step 3: Regenerating key...
%PHP_CMD% artisan key:generate --force

echo Step 4: Database check...
if not exist "database\database.sqlite" type nul > "database\database.sqlite"
%PHP_CMD% artisan migrate --force

echo Step 5: Optimizing application...
%PHP_CMD% artisan config:cache
%PHP_CMD% artisan route:cache

echo Step 6: Publishing assets...
%PHP_CMD% artisan cms:publish:assets 2>nul

echo ✓ Complete system repair finished
echo.
echo Testing application...
powershell -Command "try { $response = Invoke-WebRequest -Uri '%APP_URL%' -UseBasicParsing -TimeoutSec 5; Write-Host 'Application Status:' $response.StatusCode } catch { Write-Host 'Application Error:' $_.Exception.Message }"

goto :end

:test_connectivity
echo.
echo Testing Application Connectivity...
echo ===============================================

echo Testing main application...
powershell -Command "try { $response = Invoke-WebRequest -Uri '%APP_URL%' -UseBasicParsing -TimeoutSec 10; Write-Host 'Main App - Status:' $response.StatusCode; Write-Host 'Content Length:' $response.Content.Length } catch { Write-Host 'Main App Error:' $_.Exception.Message }"

echo.
echo Testing admin panel...
powershell -Command "try { $response = Invoke-WebRequest -Uri '%APP_URL%/admin' -UseBasicParsing -TimeoutSec 10; Write-Host 'Admin Panel - Status:' $response.StatusCode } catch { Write-Host 'Admin Panel Error:' $_.Exception.Message }"

echo.
echo Testing PHP info page...
powershell -Command "try { $response = Invoke-WebRequest -Uri '%APP_URL%/phpinfo.php' -UseBasicParsing -TimeoutSec 10; Write-Host 'PHP Info - Status:' $response.StatusCode } catch { Write-Host 'PHP Info Error:' $_.Exception.Message }"

goto :end

:invalid_choice
echo Invalid choice. Please try again.

:end
echo.
echo Troubleshooting completed.
pause
