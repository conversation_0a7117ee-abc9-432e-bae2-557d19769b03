<?php

namespace Bo<PERSON>ble\ACL\Http\Controllers\Auth;

use Botble\ACL\Forms\Auth\ForgotPasswordForm;
use Botble\ACL\Traits\SendsPasswordResetEmails;
use Bo<PERSON>ble\Base\Http\Controllers\BaseController;
use Illuminate\Http\Request;

class ForgotPasswordController extends BaseController
{
    use SendsPasswordResetEmails;

    public function __construct()
    {
        $this->middleware('guest');
    }

    public function showLinkRequestForm()
    {
        $this->pageTitle(trans('core/acl::auth.forgot_password.title'));

        return ForgotPasswordForm::create()->renderForm();
    }

    protected function sendResetLinkResponse(Request $request, $response)
    {
        return $this
            ->httpResponse()
            ->setMessage(trans($response))
            ->toResponse($request);
    }
}
