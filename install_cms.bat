@echo off
setlocal enabledelayedexpansion
color 0B
title Botble CMS Installation Helper

echo ===============================================
echo        BOTBLE CMS INSTALLATION HELPER
echo ===============================================
echo.

set "APP_PATH=c:\xampp\htdocs\main"
set "XAMPP_PATH=c:\xampp"
set "PHP_CMD=%XAMPP_PATH%\php\php.exe"

cd /d "%APP_PATH%"

echo This script will help you install Botble CMS.
echo.
echo Choose installation method:
echo 1. Automatic CLI Installation (Recommended)
echo 2. Manual Web Installation
echo 3. Reset and Reinstall
echo.
set /p choice="Enter your choice (1-3): "

if "%choice%"=="1" goto :cli_install
if "%choice%"=="2" goto :web_install
if "%choice%"=="3" goto :reset_install
goto :invalid_choice

:cli_install
echo.
echo Starting CLI installation...
echo ===============================================

echo Step 1: Clearing cache and config...
%PHP_CMD% artisan config:clear
%PHP_CMD% artisan cache:clear
%PHP_CMD% artisan route:clear

echo.
echo Step 2: Running CMS installation...
%PHP_CMD% artisan cms:install

if errorlevel 1 (
    echo.
    echo Installation failed. Trying alternative method...
    goto :manual_setup
) else (
    echo.
    echo ✓ CMS installation completed successfully!
    goto :installation_complete
)

:web_install
echo.
echo Web Installation Selected
echo ===============================================
echo.
echo 1. Make sure XAMPP Apache is running
echo 2. Open your browser and go to: http://localhost/main
echo 3. Follow the installation wizard
echo 4. Use these database settings if prompted:
echo    - Database Type: SQLite (recommended)
echo    - Or MySQL with these settings:
echo      Host: localhost
echo      Port: 3306
echo      Username: root
echo      Password: (leave empty for default XAMPP)
echo.
echo Press any key to open the installation page...
pause >nul
start "" "http://localhost/main"
goto :end

:reset_install
echo.
echo Reset and Reinstall
echo ===============================================
echo.
echo WARNING: This will delete all existing data!
set /p confirm="Are you sure? Type 'YES' to continue: "
if not "%confirm%"=="YES" goto :end

echo.
echo Resetting database...
if exist "database\database.sqlite" del "database\database.sqlite"
type nul > "database\database.sqlite"

echo Clearing all caches...
%PHP_CMD% artisan config:clear
%PHP_CMD% artisan cache:clear
%PHP_CMD% artisan route:clear
%PHP_CMD% artisan view:clear

echo Running fresh migrations...
%PHP_CMD% artisan migrate:fresh --force

echo Installing CMS...
%PHP_CMD% artisan cms:install

goto :installation_complete

:manual_setup
echo.
echo Manual Setup Required
echo ===============================================
echo.
echo Running manual setup steps...

echo 1. Creating admin user...
%PHP_CMD% artisan make:user <EMAIL> "Admin User" 159357 --role=admin 2>nul

echo 2. Setting up basic CMS data...
%PHP_CMD% artisan db:seed --class=DatabaseSeeder 2>nul

echo 3. Publishing CMS assets...
%PHP_CMD% artisan cms:publish:assets

echo 4. Optimizing application...
%PHP_CMD% artisan optimize

:installation_complete
echo.
echo ===============================================
echo         INSTALLATION COMPLETED!
echo ===============================================
echo.
echo 🌐 Your application is ready at:
echo    http://localhost/main
echo.
echo 🔑 Admin Panel Access:
echo    URL: http://localhost/main/admin
echo    Email: <EMAIL>
echo    Password: 159357
echo.
echo 📋 Next Steps:
echo 1. Visit your website: http://localhost/main
echo 2. Login to admin panel: http://localhost/main/admin
echo 3. Customize your site settings
echo 4. Change the default admin password
echo.

echo Press any key to open your website...
pause >nul
start "" "http://localhost/main"
goto :end

:invalid_choice
echo Invalid choice. Please run the script again.

:end
echo.
pause
