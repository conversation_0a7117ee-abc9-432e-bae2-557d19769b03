(()=>{"use strict";$((function(){$(document).on("click",".btn-trigger-cleanup",(function(t){t.preventDefault(),$("#cleanup-modal").modal("show")})),$(document).on("click","#cleanup-submit-action",(function(t){t.preventDefault(),t.stopPropagation();var n=$(t.currentTarget);Botble.showButtonLoading(n);var o=$("#form-cleanup-database"),a=$("#cleanup-modal");$httpClient.make().post(o.prop("action"),new FormData(o[0])).then((function(t){var n=t.data;return Botble.showSuccess(n.message)})).finally((function(){Botble.hideButtonLoading(n),a.modal("hide")}))}))}))})();